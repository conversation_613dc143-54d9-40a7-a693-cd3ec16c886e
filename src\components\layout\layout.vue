<template>
  <div class="relative overflow-y-hidden">
    <Layer />
    <left />
    <right />
    <top />
    <buttom />
    <!--    <test />-->
    <div class="w-full h-full">
      <slot name="scene"></slot>
    </div>
  </div>
</template>
<script setup>
  import Layer from '../layer/layer.vue'
  import left from './left/left.vue'
  import right from './right/right.vue'
  import top from './top/top.vue'
  import buttom from '@/components/layout/bottom/buttom.vue'
  import test from './test/test.vue'
</script>
<style setup></style>
