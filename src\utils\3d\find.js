/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/24 16时53分32秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 查找模型文集合
 */
// 创建一个函数，用于遍历 group 并返回所有 CSS3DObject 对象
import * as THREE from "three";
import {CSS3DObject} from "three/addons/renderers/CSS3DRenderer.js";
import {useMainStore} from "@/store/entra/index.js";

//找css气泡
function findCSS3DObjects(group, callback) {
    let css3DObjects = [];

    group.traverse((child) => {
        if (child instanceof CSS3DObject) {
            css3DObjects.push(child);
        }
    });

    callback(css3DObjects);
}

/**
 * 控制气泡显隐
 * @param collection 集合
 * @param boolean 是否显示
 */
function findCSS3DObjectsToVisible(collection, boolean) {
    const mainStore = useMainStore();
    findCSS3DObjects(collection, function (group) {
        group.forEach(function (item) {
            item.visible = boolean;
        });
    });
}

export {findCSS3DObjects, findCSS3DObjectsToVisible};
