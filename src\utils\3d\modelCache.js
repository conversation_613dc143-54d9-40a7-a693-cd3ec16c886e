/*
 * @Author: copilot
 * @Date: 2025/4/16
 * @Description: 3D模型缓存服务
 */

/**
 * 3D模型缓存管理器
 * 使用IndexedDB存储已加载的模型数据
 */
class ModelCacheManager {
  constructor() {
    this.dbName = 'modelCacheDB';
    this.storeName = 'modelCache';
    this.db = null;
    this.dbVersion = 1;
    this.maxCacheAge = 7 * 24 * 60 * 60 * 1000; // 默认缓存7天
    this.maxMemoryCacheSize = 1024 * 1024 * 1024; // 内存缓存上限1GB
    this.currentMemorySize = 0; // 当前内存使用量
    this.loadingQueue = new Map(); // 加载队列
    this.retryAttempts = 3; // 加载重试次数
    this.retryDelay = 1000; // 重试延迟（毫秒）
    this.init();
    
    // 定期检查内存使用情况
    setInterval(() => this.checkMemoryUsage(), 60000);
  }

  /**
   * 初始化数据库
   */
  init() {
    const request = indexedDB.open(this.dbName, this.dbVersion);
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains(this.storeName)) {
        const store = db.createObjectStore(this.storeName, { keyPath: 'url' });
        store.createIndex('timestamp', 'timestamp', { unique: false });
        store.createIndex('version', 'version', { unique: false });
        store.createIndex('size', 'size', { unique: false });
      }
    };

    request.onsuccess = (event) => {
      this.db = event.target.result;
      console.log('模型缓存数据库初始化成功');
      
      // 数据库打开后，清理过期缓存
      this.cleanExpiredCache();
    };

    request.onerror = (event) => {
      console.error('模型缓存数据库初始化失败:', event.target.error);
    };
  }

  /**
   * 检查内存使用情况并在必要时清理
   * @private
   */
  async checkMemoryUsage() {
    if (this.currentMemorySize > this.maxMemoryCacheSize * 0.8) {
      console.warn('内存缓存接近上限，开始清理最少使用的模型');
      await this.cleanLeastUsedCache();
    }
  }

  /**
   * 清理最少使用的缓存直到内存使用量降到阈值以下
   * @private
   */
  async cleanLeastUsedCache() {
    const cacheEntries = Array.from(memoryCache.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
    
    while (this.currentMemorySize > this.maxMemoryCacheSize * 0.6 && cacheEntries.length > 0) {
      const [url, model] = cacheEntries.shift();
      const modelSize = this.estimateModelSize(model);
      this.currentMemorySize -= modelSize;
      memoryCache.delete(url);
    }
  }

  /**
   * 估算模型大小
   * @param {Object} model 模型对象
   * @returns {number} 估算的字节数
   * @private
   */
  estimateModelSize(model) {
    let size = 0;
    if (model.scene) {
      model.scene.traverse((object) => {
        if (object.geometry) {
          const geometrySize = object.geometry.attributes.position.array.length * 4;
          size += geometrySize;
        }
        if (object.material) {
          // 估算材质大小（贴图等）
          if (object.material.map) size += 1024 * 1024; // 假设每个贴图约1MB
        }
      });
    }
    return size || JSON.stringify(model).length;
  }

  /**
   * 获取模型，支持并发控制和重试机制
   * @param {string} url 模型URL
   * @returns {Promise<Object|null>} 缓存的模型数据
   */
  async getModel(url) {
    // 检查是否已经在加载队列中
    if (this.loadingQueue.has(url)) {
      return this.loadingQueue.get(url);
    }

    const loadPromise = this._getModelWithRetry(url);
    this.loadingQueue.set(url, loadPromise);

    try {
      const result = await loadPromise;
      this.loadingQueue.delete(url);
      return result;
    } catch (error) {
      this.loadingQueue.delete(url);
      throw error;
    }
  }

  /**
   * 带重试机制的模型获取
   * @param {string} url 模型URL
   * @param {number} attempt 当前重试次数
   * @returns {Promise<Object|null>}
   * @private
   */
  async _getModelWithRetry(url, attempt = 1) {
    try {
      const cachedData = await this._getModelFromDB(url);
      if (cachedData) {
        return cachedData;
      }
      return null;
    } catch (error) {
      if (attempt < this.retryAttempts) {
        console.warn(`获取缓存模型失败，${this.retryDelay/1000}秒后重试(${attempt}/${this.retryAttempts}):`, error);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        return this._getModelWithRetry(url, attempt + 1);
      }
      throw error;
    }
  }

  /**
   * 从数据库获取模型
   * @param {string} url 模型URL
   * @returns {Promise<Object|null>}
   * @private
   */
  async _getModelFromDB(url) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        resolve(null);
        return;
      }

      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(url);

      request.onsuccess = (event) => {
        const result = event.target.result;
        if (result && (Date.now() - result.timestamp) < this.maxCacheAge) {
          // 验证缓存完整性
          if (this.validateCache(result)) {
            this.updateTimestamp(url);
            resolve(result.data);
          } else {
            console.warn(`缓存数据完整性验证失败: ${url}`);
            this.removeModel(url);
            resolve(null);
          }
        } else {
          if (result) {
            this.removeModel(url);
          }
          resolve(null);
        }
      };

      request.onerror = (event) => {
        console.error('获取缓存模型失败:', event.target.error);
        reject(event.target.error);
      };
    });
  }

  /**
   * 验证缓存数据完整性
   * @param {Object} cacheEntry 缓存条目
   * @returns {boolean} 是否有效
   * @private
   */
  validateCache(cacheEntry) {
    if (!cacheEntry || !cacheEntry.data) return false;
    
    // 检查必要的数据结构
    if (!cacheEntry.data.scene || !cacheEntry.checksum) {
      return false;
    }

    // 验证校验和
    const currentChecksum = this.calculateChecksum(cacheEntry.data);
    return currentChecksum === cacheEntry.checksum;
  }

  /**
   * 计算数据校验和
   * @param {Object} data 要计算校验和的数据
   * @returns {string} 校验和
   * @private
   */
  calculateChecksum(data) {
    // 简单实现，实际应使用更复杂的哈希算法
    return btoa(JSON.stringify(data)).slice(0, 32);
  }

  /**
   * 缓存模型数据
   * @param {string} url 模型URL
   * @param {Object} data 模型数据
   * @returns {Promise<boolean>} 是否成功缓存
   */
  async cacheModel(url, data) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        this.init();
        resolve(false);
        return;
      }

      const size = this.estimateModelSize(data);
      
      // 如果单个模型过大，不进行持久化缓存
      if (size > 50 * 1024 * 1024) {
        console.warn(`模型太大，不进行持久化缓存: ${url} (约 ${Math.round(size/1024/1024)}MB)`);
        resolve(false);
        return;
      }

      try {
        const cacheEntry = {
          url,
          data,
          timestamp: Date.now(),
          version: '1.0', // 添加版本信息
          size,
          checksum: this.calculateChecksum(data)
        };

        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.put(cacheEntry);

        request.onsuccess = () => {
          console.log(`模型缓存成功: ${url}`);
          resolve(true);
        };

        request.onerror = (event) => {
          console.error('缓存模型失败:', event.target.error);
          resolve(false);
        };
      } catch (error) {
        console.error('缓存模型时发生错误:', error);
        resolve(false);
      }
    });
  }

  /**
   * 更新模型的时间戳
   * @param {string} url 模型URL
   * @private
   */
  updateTimestamp(url) {
    if (!this.db) return;

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);
    const request = store.get(url);

    request.onsuccess = (event) => {
      const data = event.target.result;
      if (data) {
        data.timestamp = Date.now();
        store.put(data);
      }
    };
  }

  /**
   * 删除缓存的模型
   * @param {string} url 模型URL
   * @returns {Promise<boolean>}
   */
  async removeModel(url) {
    return new Promise((resolve) => {
      if (!this.db) {
        resolve(false);
        return;
      }

      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(url);

      request.onsuccess = () => {
        console.log(`删除缓存模型: ${url}`);
        resolve(true);
      };

      request.onerror = () => {
        resolve(false);
      };
    });
  }

  /**
   * 清理过期的缓存
   */
  async cleanExpiredCache() {
    if (!this.db) return;

    const now = Date.now();
    const expireTime = now - this.maxCacheAge;
    
    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);
    const index = store.index('timestamp');
    
    // 查找过期的条目
    const range = IDBKeyRange.upperBound(expireTime);
    const request = index.openCursor(range);
    
    request.onsuccess = (event) => {
      const cursor = event.target.result;
      if (cursor) {
        // 删除过期条目
        store.delete(cursor.primaryKey);
        cursor.continue();
      }
    };
  }

  /**
   * 清空所有缓存
   * @returns {Promise<boolean>}
   */
  async clearCache() {
    return new Promise((resolve) => {
      if (!this.db) {
        resolve(false);
        return;
      }

      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.clear();

      request.onsuccess = () => {
        console.log('已清空所有模型缓存');
        resolve(true);
      };

      request.onerror = () => {
        resolve(false);
      };
    });
  }
}

// 创建单例实例
const modelCache = new ModelCacheManager();

export default modelCache;