<template>
  <use-charts :options="option" class="h-full w-full"></use-charts>
</template>

<style scoped></style>
<script setup name="ManageRight1">
  import UseCharts from '@/common/useCharts.vue'

  let data = [
    {
      value: 252,
      name: '住院收入',
    },
    {
      value: 168,
      name: '门诊收入',
    },
    {
      value: 118,
      name: '计数服务',
    },
    {
      value: 134,
      name: '药物收入',
    },
    {
      value: 101,
      name: '挂号',
    },
  ]

  let arrName = getArrayValue(data, 'name')
  let arrValue = getArrayValue(data, 'value')
  let sumValue = eval(arrValue.join('+'))
  let objData = array2obj(data, 'name')
  let optionData = getData(data)

  function getArrayValue(array, key) {
    key = key || 'value'
    let res = []
    if (array) {
      array.forEach(function (t) {
        res.push(t[key])
      })
    }
    return res
  }

  function array2obj(array, key) {
    let resObj = {}
    for (let i = 0; i < array.length; i++) {
      resObj[array[i][key]] = array[i]
    }
    return resObj
  }

  function getData(data) {
    let res = {
      series: [],
      yAxis: [],
    }
    for (let i = 0; i < data.length; i++) {
      // console.log([70 - i * 15 + '%', 67 - i * 15 + '%']);
      res.series.push({
        name: '',
        type: 'pie',
        clockWise: false, //顺时加载
        hoverAnimation: false, //鼠标移入变大
        radius: [73 - i * 15 + '%', 68 - i * 15 + '%'],
        center: ['30%', '40%'],
        label: {
          show: false,
        },
        itemStyle: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          borderWidth: 5,
        },
        data: [
          {
            value: data[i].value,
            name: data[i].name,
          },
          {
            value: sumValue - data[i].value,
            name: '',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
              borderWidth: 0,
            },
            tooltip: {
              show: false,
            },
            hoverAnimation: false,
          },
        ],
      })
      res.series.push({
        name: '',
        type: 'pie',
        silent: true,
        z: 1,
        clockWise: false, //顺时加载
        hoverAnimation: false, //鼠标移入变大
        radius: [73 - i * 15 + '%', 68 - i * 15 + '%'],
        center: ['30%', '40%'],
        label: {
          show: false,
        },
        itemStyle: {
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          borderWidth: 5,
        },
        data: [
          {
            value: 7.5,
            itemStyle: {
              color: 'rgb(3, 31, 62)',
              borderWidth: 0,
            },
            tooltip: {
              show: false,
            },
            hoverAnimation: false,
          },
          {
            value: 2.5,
            name: '',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
              borderWidth: 0,
            },
            tooltip: {
              show: false,
            },
            hoverAnimation: false,
          },
        ],
      })
      res.yAxis.push(((data[i].value / sumValue) * 100).toFixed(2) + '%')
    }
    return res
  }

  const option = {
    backgroundColor: '',
    legend: {
      show: true,
      icon: 'circle',
      top: '0%',
      left: '60%',
      data: arrName,
      width: 30,
      padding: [0, 2],
      itemGap: 5,
      formatter: function (name) {
        return '{title|' + name + '}\n{value|' + objData[name].value + '}  {title|万}'
      },

      textStyle: {
        rich: {
          title: {
            fontSize: 12,
            lineHeight: 15,
            color: 'rgb(0, 178, 246)',
          },
          value: {
            fontSize: 14,
            lineHeight: 10,
            color: '#fff',
          },
        },
      },
    },
    tooltip: {
      show: false,
      trigger: 'item',
      formatter: '{a}<br>{b}:{c}({d}%)',
    },
    color: ['rgb(24, 183, 142)', 'rgb(1, 179, 238)', 'rgb(22, 75, 205)', 'rgb(52, 52, 176)'],
    grid: {
      top: '16%',
      bottom: '53%',
      left: '30%',
      containLabel: false,
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLine: {
          show: false,
        },

        axisTick: {
          show: false,
        },
        axisLabel: {
          interval: 0,
          inside: true,
          textStyle: {
            color: '#fff',
            fontSize: 16,
          },
          show: false,
        },
        data: optionData.yAxis,
      },
    ],
    xAxis: [
      {
        show: false,
      },
    ],
    series: optionData.series,
  }
</script>
