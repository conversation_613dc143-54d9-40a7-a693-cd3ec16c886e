<template>
  <div class="container">
    <div class="item" v-for="item in items" :key="item.id">
      <div class="name">{{ item.title }}</div>
      <div>{{ item.value }}</div>
    </div>
  </div>
  <div class="text-[16px] text-[#20c9ce] mt-[1px]">12h进出流量</div>
  <div class="w-[100%] h-[100px]">
    <use-charts :options="option" class="h-full w-full"></use-charts>
  </div>
</template>

<script setup name="ManageLeft1">
  import UseCharts from '@/common/useCharts.vue'
  import * as echarts from 'echarts'

  const items = [
    { id: 1, title: '进入院区人次', value: 830 },
    { id: 2, title: '出去院区人次', value: 940 },
    { id: 3, title: '门诊人次', value: 1703 },
  ]

  const colorList = ['#9E87FF', '#73DDFF', '#fe9a8b', '#F56948', '#9E87FF']
  const option = {
    backgroundColor: '',
    legend: {
      icon: 'circle',
      top: '-30%',
      right: '10%',
      itemWidth: 6,
      itemGap: 5,
      textStyle: {
        color: '#fff',
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        label: {
          show: true,
          backgroundColor: '#fff',
          color: '#000',
          borderColor: 'rgba(0,0,0,0)',
          shadowColor: 'rgba(0,0,0,0)',
          shadowOffsetY: 0,
        },
        lineStyle: {
          width: 0,
        },
      },
      formatter: function (params) {
        return `${params[0].name}月<br/>
        ${params[0].seriesName}:${params[0].value}人次<br/>
        ${params[1].seriesName}:${params[1].value}人次<br/>
        ${params[2].seriesName}:${params[2].value}次`
      },
      /*  backgroundColor: "",
    textStyle: {
      color: "#fff",
    },*/
      // padding: [10, 10],
      // extraCssText: "box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)",
    },
    grid: {
      top: '0%',
      right: '13%',
      left: '10%',
      bottom: '40%',
    },
    xAxis: [
      {
        type: 'category',
        data: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        axisLine: {
          lineStyle: {
            color: '#DCE2E8',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          interval: 0,
          textStyle: {
            color: '#fff',
          },
          // 默认x轴字体大小
          fontSize: 12,
          // margin:文字到x轴的距离
          margin: 5,
        },
        axisPointer: {
          label: {
            // padding: [11, 5, 7],
            padding: [0, 0, 10, 0],
            // 这里的margin和axisLabel的margin要一致!
            margin: 15,
            // 移入时的字体大小
            fontSize: 12,
            backgroundColor: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#fff', // 0% 处的颜色
                },
                {
                  // offset: 0.9,
                  offset: 0.86,
                  /*
0.86 = （文字 + 文字距下边线的距离）/（文字 + 文字距下边线的距离 + 下边线的宽度）

            */
                  color: '#fff', // 0% 处的颜色
                },
                {
                  offset: 0.86,
                  color: '#33c0cd', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#33c0cd', // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
          },
        },
        boundaryGap: false,
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#DCE2E8',
          },
        },
        axisLabel: {
          textStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
      },
      {
        type: 'value',
        position: 'right',
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          textStyle: {
            color: '#556677',
          },
          formatter: '{value}',
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#DCE2E8',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '门诊',
        type: 'line',
        data: [45, 59, 52, 37, 110, 39, 57, 89, 109, 80, 10, 112],
        symbolSize: 1,
        symbol: 'circle',
        smooth: true,
        yAxisIndex: 0,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            {
              offset: 0,
              color: '#9effff',
            },
            {
              offset: 1,
              color: '#9E87FF',
            },
          ]),
          shadowColor: 'rgba(158,135,255, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 20,
        },
        itemStyle: {
          normal: {
            color: colorList[0],
            borderColor: colorList[0],
          },
        },
      },
      {
        name: '住院',
        type: 'line',
        data: [36, 43, 35, 42, 25, 29, 49, 13, 40, 13, 18, 23],
        symbolSize: 1,
        symbol: 'circle',
        smooth: true,
        yAxisIndex: 0,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: new echarts.graphic.LinearGradient(1, 1, 0, 0, [
            {
              offset: 0,
              color: '#73DD39',
            },
            {
              offset: 1,
              color: '#73DDFF',
            },
          ]),
          shadowColor: 'rgba(115,221,255, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 20,
        },
        itemStyle: {
          normal: {
            color: colorList[1],
            borderColor: colorList[1],
          },
        },
      },
      {
        name: '出院',
        type: 'line',
        data: [21, 39, 37, 10, 43, 43, 18, 18, 43, 39, 43, 17],
        symbolSize: 1,
        yAxisIndex: 1,
        symbol: 'circle',
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: '#fe9a',
            },
            {
              offset: 1,
              color: '#fe9a8b',
            },
          ]),
          shadowColor: 'rgba(254,154,139, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 20,
        },
        itemStyle: {
          normal: {
            color: colorList[2],
            borderColor: colorList[2],
          },
        },
      },
    ],
  }
</script>
<style scoped>
  .container {
    display: flex;
    justify-content: space-around;
    color: #fff;
  }

  .item {
    text-align: center;
    flex: 1;
  }

  .name {
    font-size: 16px;
    color: #92a6b8;
  }
</style>
