/**
 * Description:数据存储
 * Author: du<PERSON>zhu
 * Date: 2022/8/10
 */
import { defineStore } from 'pinia'
import { markRaw, ref } from 'vue'
import { drawLine } from '@/utils/3d/drawLine.js'
import * as THREE from 'three'

export const useMainStore = defineStore({
  id: 'obj',
  state: () => ({
    scene: null, //场景
    camera: null, //相机,
    renderer: null, //渲染组件
    model: null, //模型集合 需要检测事件的
    renderFunc: markRaw([]),
    gui: null, //gui
    guiTest: {
      drawing: false, //是否开启绘制
    }, //添加一些gui测试值 需要存储的
    draw: {
      cruisingCollection: null, //绘制对象集合
      pointArrays: markRaw([]), //点存储地方
      pathArray: markRaw([]), //存储路径
      openRight: false, //开启右键事件
    }, //绘制类存储位置
    bubble: {
      labelDom: null,
      underGroundInfoObject: null, //停车厂区域的实例化对象
      underGroundCameraObject: null, //地下车库监控
      indoorObject: null, //室内设置
    }, //newBubble的实力化对象
    bubbles: {
      underGroundInfoCollection: null, //停车区域的气泡集合
      underGroundCameraCollection: null, //地下车库监控集合
      indoorCollection: null, //室内气泡的集合对象
    }, //场景中气泡的集合
    eventDom: null, //点击对象事件
    underGroundSceneObject: null, //地库对象集合
    queryCarByName: '', //查询汽车的名称
  }),
  getters: {},
  //处理业务逻辑
  actions: {
    startDrawLine() {
      return {
        drawLine: new drawLine(),
      }
    },
    //开启右键保存
    setSavePath(boolean) {
      this.draw.openRight = boolean
    },
  },
})
