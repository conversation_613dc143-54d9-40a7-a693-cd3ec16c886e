/*
 * @Author: copilot
 * @Date: 2025/4/16
 * @Description: 3D模型序列化工具
 */
import * as THREE from 'three';

/**
 * 处理模型序列化和反序列化的工具类
 */
class ModelSerializer {
  /**
   * 序列化GLTF模型数据以便存储
   * @param {Object} gltf GLTF模型对象
   * @returns {Object} 可序列化的对象
   */
  static serializeGLTF(gltf) {
    // 创建一个可序列化的对象
    const serialized = {
      scene: this.serializeObject3D(gltf.scene),
      animations: gltf.animations ? gltf.animations.map(anim => this.serializeAnimation(anim)) : [],
      asset: gltf.asset,
      parser: {
        json: gltf.parser?.json,
        associations: this.serializeAssociations(gltf.parser?.associations)
      },
      userData: gltf.userData
    };
    
    return serialized;
  }
  
  /**
   * 反序列化GLTF数据为Three.js对象
   * @param {Object} data 序列化的数据
   * @returns {Object} 反序列化的GLTF对象
   */
  static deserializeGLTF(data) {
    // 创建一个新的GLTF结构
    const gltf = {
      scene: this.deserializeObject3D(data.scene),
      animations: data.animations.map(anim => this.deserializeAnimation(anim)),
      asset: data.asset,
      parser: {
        json: data.parser?.json,
        associations: this.deserializeAssociations(data.parser?.associations)
      },
      userData: data.userData
    };
    
    // 恢复场景中的层级关系
    this.reconstructHierarchy(gltf.scene, data.scene);
    
    return gltf;
  }
  
  /**
   * 序列化Three.js的Object3D对象
   * @param {THREE.Object3D} object 
   * @returns {Object}
   * @private
   */
  static serializeObject3D(object) {
    if (!object) return null;
    
    const serialized = {
      uuid: object.uuid,
      type: object.constructor.name,
      name: object.name,
      visible: object.visible,
      matrix: Array.from(object.matrix.elements),
      position: { x: object.position.x, y: object.position.y, z: object.position.z },
      quaternion: { 
        x: object.quaternion.x, 
        y: object.quaternion.y, 
        z: object.quaternion.z, 
        w: object.quaternion.w 
      },
      scale: { x: object.scale.x, y: object.scale.y, z: object.scale.z },
      userData: object.userData,
      children: object.children.map(child => this.serializeObject3D(child))
    };

    // 处理特定类型的对象
    if (object.isMesh) {
      serialized.geometry = this.serializeGeometry(object.geometry);
      serialized.material = this.serializeMaterial(object.material);
    }
    
    return serialized;
  }
  
  /**
   * 反序列化成Three.js的Object3D对象
   * @param {Object} data 
   * @returns {THREE.Object3D}
   * @private
   */
  static deserializeObject3D(data) {
    if (!data) return null;
    
    let object;
    
    // 根据类型创建相应的对象
    switch (data.type) {
      case 'Scene':
        object = new THREE.Scene();
        break;
      case 'Group':
        object = new THREE.Group();
        break;
      case 'Mesh':
        const geometry = this.deserializeGeometry(data.geometry);
        const material = this.deserializeMaterial(data.material);
        object = new THREE.Mesh(geometry, material);
        break;
      default:
        object = new THREE.Object3D();
    }
    
    // 设置基本属性
    object.uuid = data.uuid;
    object.name = data.name;
    object.visible = data.visible;
    
    // 设置变换属性
    if (data.matrix) {
      object.matrix.fromArray(data.matrix);
      object.matrix.decompose(object.position, object.quaternion, object.scale);
    } else {
      if (data.position) object.position.set(data.position.x, data.position.y, data.position.z);
      if (data.quaternion) object.quaternion.set(data.quaternion.x, data.quaternion.y, data.quaternion.z, data.quaternion.w);
      if (data.scale) object.scale.set(data.scale.x, data.scale.y, data.scale.z);
    }
    
    // 设置用户数据
    object.userData = data.userData || {};
    
    return object;
  }
  
  /**
   * 重建对象层级结构
   * @param {THREE.Object3D} parent 
   * @param {Object} parentData 
   * @private
   */
  static reconstructHierarchy(parent, parentData) {
    if (!parentData.children || !parent) return;
    
    parentData.children.forEach((childData, index) => {
      const child = this.deserializeObject3D(childData);
      parent.add(child);
      
      // 递归处理子级
      this.reconstructHierarchy(child, childData);
    });
  }
  
  /**
   * 序列化几何体
   * @param {THREE.BufferGeometry} geometry 
   * @returns {Object}
   * @private
   */
  static serializeGeometry(geometry) {
    if (!geometry) return null;
    
    // 我们只存储基本信息，详细数据会在原始加载中恢复
    return {
      uuid: geometry.uuid,
      type: geometry.type
    };
  }
  
  /**
   * 反序列化几何体
   * @param {Object} data 
   * @returns {THREE.BufferGeometry}
   * @private
   */
  static deserializeGeometry(data) {
    if (!data) return null;
    return new THREE.BufferGeometry();
  }
  
  /**
   * 序列化材质
   * @param {THREE.Material|Array} material 
   * @returns {Object|Array}
   * @private
   */
  static serializeMaterial(material) {
    if (!material) return null;
    
    // 如果是材质数组
    if (Array.isArray(material)) {
      return material.map(mat => this.serializeMaterial(mat));
    }
    
    // 单个材质，只存储基本信息
    return {
      uuid: material.uuid,
      type: material.type,
      name: material.name
    };
  }
  
  /**
   * 反序列化材质
   * @param {Object|Array} data 
   * @returns {THREE.Material|Array}
   * @private
   */
  static deserializeMaterial(data) {
    if (!data) return null;
    
    if (Array.isArray(data)) {
      return data.map(matData => this.deserializeMaterial(matData));
    }
    
    return new THREE.MeshBasicMaterial();
  }
  
  /**
   * 序列化动画
   * @param {THREE.AnimationClip} animation 
   * @returns {Object}
   * @private
   */
  static serializeAnimation(animation) {
    if (!animation) return null;
    
    return {
      name: animation.name,
      duration: animation.duration
    };
  }
  
  /**
   * 反序列化动画
   * @param {Object} data 
   * @returns {THREE.AnimationClip}
   * @private
   */
  static deserializeAnimation(data) {
    if (!data) return null;
    
    // 创建一个空的动画片段
    return new THREE.AnimationClip(data.name, data.duration, []);
  }
  
  /**
   * 序列化关联信息
   * @param {Map} associations 
   * @returns {Array}
   * @private
   */
  static serializeAssociations(associations) {
    if (!associations) return null;
    
    // 将Map转换为可序列化的数组
    const result = [];
    if (associations instanceof Map) {
      associations.forEach((value, key) => {
        // 如果键是对象，则存储其uuid
        const keyId = key.uuid || '';
        result.push([keyId, value]);
      });
    }
    
    return result;
  }
  
  /**
   * 反序列化关联信息
   * @param {Array} data 
   * @returns {Map}
   * @private
   */
  static deserializeAssociations(data) {
    if (!data) return null;
    
    const associations = new Map();
    
    // 在实际使用时会重建这些关联，此处返回空Map
    return associations;
  }
}

export default ModelSerializer;