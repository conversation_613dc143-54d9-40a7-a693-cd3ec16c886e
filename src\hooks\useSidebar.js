import {ref} from "vue";

const leftVisible = ref(true);
const rightVisible = ref(true);

/**
 * 两边的栏目 显示隐藏
 * @returns {{setVisible: (function(*): void), toggleRight: (function(): void), rightVisible: Ref<UnwrapRef<boolean>>, toggleLeft: (function(): void), leftVisible: Ref<UnwrapRef<boolean>>}}
 */
export const useSidebarVisible = function () {
    const toggleLeft = () => {
        leftVisible.value = !leftVisible.value;
    };
    const toggleRight = () => {
        rightVisible.value = !rightVisible.value;
    };
    const setVisible = (bool) => {
        leftVisible.value = bool;
        rightVisible.value = bool;
    };
    return {
        leftVisible,
        rightVisible,
        toggleLeft,
        toggleRight,
        setVisible,
    };
};
