{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 531, "max": [0.8545699715614319, 2.8467299938201904, 0.9076300263404846], "min": [-0.8545699715614319, 1.6928499937057495, 0.5023999810218811], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6372, "componentType": 5126, "count": 531, "max": [1.0, 1.0, 0.9710734486579895], "min": [-1.0, -1.0, -3.0000224796822295e-05], "type": "VEC3"}, {"bufferView": 0, "componentType": 5125, "count": 2640, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12744, "componentType": 5126, "count": 147, "max": [1.1231600046157837, -0.27368998527526855, 0.9805700182914734], "min": [-1.1231600046157837, -1.4143400192260742, 0.4009299874305725], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 14508, "componentType": 5126, "count": 147, "max": [0.4308382570743561, -0.38543087244033813, 0.893667995929718], "min": [-0.4308382570743561, -0.5051785111427307, 0.780864953994751], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 10560, "componentType": 5125, "count": 720, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 16272, "componentType": 5126, "count": 388, "max": [1.2015700340270996, -0.22436000406742096, 0.9953699707984924], "min": [-1.2015700340270996, -1.5346399545669556, 0.3159799873828888], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 20928, "componentType": 5126, "count": 388, "max": [0.9265510439872742, 0.5170151591300964, 0.9968052506446838], "min": [-0.9265510439872742, -0.9860039949417114, -0.6131961345672607], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 13440, "componentType": 5125, "count": 2004, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 25584, "componentType": 5126, "count": 1954, "max": [50.911659240722656, 0.9718700051307678, 0.11204999685287476], "min": [-0.9718899726867676, -49.900421142578125, -50.9669189453125], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 49032, "componentType": 5126, "count": 1954, "max": [0.9989358186721802, 0.8395543098449707, 1.0], "min": [-0.8395543098449707, -0.997192919254303, -0.617008626461029], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 1954, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 21456, "componentType": 5125, "count": 11136, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 72480, "componentType": 5126, "count": 1954, "max": [0.9719099998474121, 26.53162956237793, 0.11204999685287476], "min": [-25.646610260009766, -0.9719300270080566, -3.8567299842834473], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 95928, "componentType": 5126, "count": 1954, "max": [0.8395543098449707, 0.9112728238105774, 1.0], "min": [-0.9084474444389343, -0.8395543098449707, 0.3564639091491699], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 15632, "componentType": 5126, "count": 1954, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 66000, "componentType": 5125, "count": 11136, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 119376, "componentType": 5126, "count": 721, "max": [0.795799970626831, 0.7957500219345093, 0.11906000226736069], "min": [-0.795769989490509, -0.7958099842071533, 0.08641999959945679], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 128028, "componentType": 5126, "count": 721, "max": [0.9252393841743469, 0.9252393841743469, 1.0], "min": [-0.9252393841743469, -0.9252393841743469, 0.3788357973098755], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 31264, "componentType": 5126, "count": 721, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 110544, "componentType": 5125, "count": 4032, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 136680, "componentType": 5126, "count": 2310, "max": [1.3345799446105957, 3.7109999656677246, -0.263480007648468], "min": [-1.3345799446105957, -3.5749099254608154, -0.7409300208091736], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 164400, "componentType": 5126, "count": 2310, "max": [0.9998931288719177, 0.9993736147880554, 0.9999727606773376], "min": [-0.9998931288719177, -0.9938499331474304, -0.9999712109565735], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 126672, "componentType": 5125, "count": 12864, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 192120, "componentType": 5126, "count": 668, "max": [1.5537300109863281, 3.706700086593628, 0.5070400238037109], "min": [-1.5537300109863281, -3.5997400283813477, -0.9151700139045715], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 200136, "componentType": 5126, "count": 668, "max": [0.6869449019432068, 1.0, 1.0], "min": [-0.6869449019432068, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 178128, "componentType": 5125, "count": 3312, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 208152, "componentType": 5126, "count": 17300, "max": [1.5262199640274048, 2.5808300971984863, -0.08525999635457993], "min": [-1.5262199640274048, 1.6722999811172485, -0.9937800168991089], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 415752, "componentType": 5126, "count": 17300, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 37032, "componentType": 5126, "count": 17300, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 191376, "componentType": 5125, "count": 54288, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 623352, "componentType": 5126, "count": 10570, "max": [1.5231900215148926, 2.5604100227355957, -0.10566999763250351], "min": [-1.5231900215148926, 1.6927200555801392, -0.973360002040863], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 750192, "componentType": 5126, "count": 10570, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 175432, "componentType": 5126, "count": 10570, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 408528, "componentType": 5125, "count": 59280, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 877032, "componentType": 5126, "count": 4222, "max": [1.542240023612976, 2.6940701007843018, 0.027979999780654907], "min": [-1.542240023612976, 1.559059977531433, -1.1070200204849243], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 927696, "componentType": 5126, "count": 4222, "max": [0.9945850968360901, 0.9993732571601868, 0.9993689656257629], "min": [-0.9945850968360901, -0.9993732571601868, -0.9993689656257629], "type": "VEC3"}, {"bufferView": 3, "componentType": 5126, "count": 4222, "max": [0.944123387336731, 0.999981164932251, 0.9993739724159241, 1.0], "min": [-0.944123387336731, -0.9999921321868896, -0.9993741512298584, -1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 259992, "componentType": 5126, "count": 4222, "max": [7.409379959106445, 0.9987800121307373], "min": [-4.900440216064453, 0.0232900008559227], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 293768, "componentType": 5126, "count": 4222, "max": [7.409379959106445, 0.9987800121307373], "min": [-4.900440216064453, 0.0232900008559227], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 645648, "componentType": 5125, "count": 23040, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 978360, "componentType": 5126, "count": 3754, "max": [1.34906005859375, 1.9953399896621704, -0.28306999802589417], "min": [-1.34906005859375, 1.794119954109192, -0.8278499841690063], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1023408, "componentType": 5126, "count": 3754, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 327544, "componentType": 5126, "count": 3754, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 737808, "componentType": 5125, "count": 19968, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1068456, "componentType": 5126, "count": 4, "max": [0.34189000725746155, 0.4485499858856201, 0.0], "min": [-0.34189000725746155, -0.4485499858856201, 0.0], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1068504, "componentType": 5126, "count": 4, "max": [0.0, 0.0, 1.0], "min": [0.0, 0.0, 1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 357576, "componentType": 5126, "count": 4, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 817680, "componentType": 5125, "count": 6, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1068552, "componentType": 5126, "count": 4956, "max": [64.10942077636719, 31.948179244995117, 2.1877899169921875], "min": [-72.4798583984375, -0.6549500226974487, -20.406139373779297], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1128024, "componentType": 5126, "count": 4956, "max": [0.9998564124107361, 0.9999991059303284, 0.9999392628669739], "min": [-0.9993187189102173, -0.9999338388442993, -0.9997302293777466], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 817704, "componentType": 5125, "count": 27960, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1187496, "componentType": 5126, "count": 7396, "max": [1.3769700527191162, -2.602760076522827, 0.2973099946975708], "min": [-1.3769700527191162, -3.1870200634002686, -0.11714000254869461], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1276248, "componentType": 5126, "count": 7396, "max": [0.9954891204833984, 0.9999969601631165, 0.9997614026069641], "min": [-0.9954891204833984, -0.9999961256980896, -1.0], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 929544, "componentType": 5125, "count": 40452, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1365000, "componentType": 5126, "count": 1910, "max": [1.2473000288009644, -2.818959951400757, 0.1944199949502945], "min": [-1.2473000288009644, -2.877039909362793, -0.028769999742507935], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1387920, "componentType": 5126, "count": 1910, "max": [0.9996860027313232, 0.9970803260803223, 1.0], "min": [-0.9996860027313232, -0.9995126128196716, -1.0], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1091352, "componentType": 5125, "count": 11040, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1410840, "componentType": 5126, "count": 3538, "max": [1.348270058631897, -2.5756499767303467, 0.28057000041007996], "min": [-1.348270058631897, -3.1446800231933594, -0.08107999712228775], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1453296, "componentType": 5126, "count": 3538, "max": [0.9989157319068909, 0.7494239807128906, 0.9994263052940369], "min": [-0.9989157319068909, -0.9969171285629272, -0.9824032187461853], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1135512, "componentType": 5125, "count": 19560, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1495752, "componentType": 5126, "count": 794, "max": [1.337980031967163, -2.504270076751709, 0.2764900028705597], "min": [-1.337980031967163, -3.0833001136779785, -0.14528000354766846], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1505280, "componentType": 5126, "count": 794, "max": [0.9988709092140198, 0.821334183216095, 0.9825711846351624], "min": [-0.9988709092140198, -0.9823732376098633, -0.3683660328388214], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1213752, "componentType": 5125, "count": 4440, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1514808, "componentType": 5126, "count": 622, "max": [1.5592900514602661, -2.5425500869750977, -0.06261999905109406], "min": [-1.5592900514602661, -2.813110113143921, -0.12706999480724335], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1522272, "componentType": 5126, "count": 622, "max": [0.9994158744812012, 0.9977309107780457, 0.9997457265853882], "min": [-0.999415397644043, -0.9963476061820984, -0.9999999403953552], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 357608, "componentType": 5126, "count": 622, "max": [0.1264999955892563, 0.33493998646736145], "min": [0.04774000123143196, 0.04408000037074089], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1231512, "componentType": 5125, "count": 3120, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1529736, "componentType": 5126, "count": 448, "max": [1.3416800498962402, -3.1666600704193115, -0.25001999735832214], "min": [-1.3416800498962402, -3.658489942550659, -0.4192200005054474], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1535112, "componentType": 5126, "count": 448, "max": [0.9971323013305664, 0.6387405395507812, 0.9874165654182434], "min": [-0.9971323013305664, -0.7215074896812439, -0.9969515204429626], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1243992, "componentType": 5125, "count": 2304, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1540488, "componentType": 5126, "count": 1394, "max": [1.342210054397583, -3.177180051803589, -0.25992000102996826], "min": [-1.342210054397583, -3.6529700756073, -0.4105699956417084], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1557216, "componentType": 5126, "count": 1394, "max": [0.9969676733016968, 0.8663612008094788, 0.9999992251396179], "min": [-0.9969676733016968, -0.9991371631622314, -0.9999991059303284], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1253208, "componentType": 5125, "count": 7872, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1573944, "componentType": 5126, "count": 1404, "max": [1.2944200038909912, -3.258929967880249, -0.2676900029182434], "min": [-1.2944200038909912, -3.6061699390411377, -0.34685999155044556], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1590792, "componentType": 5126, "count": 1404, "max": [0.959891140460968, 0.7038306593894958, 0.9511206746101379], "min": [-0.959891140460968, -0.9629784822463989, -0.9999455809593201], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1284696, "componentType": 5125, "count": 7104, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1607640, "componentType": 5126, "count": 192, "max": [1.342710018157959, -3.2348499298095703, -0.2593599855899811], "min": [-1.342710018157959, -3.665560007095337, -0.41137000918388367], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1609944, "componentType": 5126, "count": 192, "max": [0.9378917813301086, 0.36299219727516174, 0.9984806180000305], "min": [-0.9378917813301086, -0.17429454624652863, -0.9998160004615784], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1313112, "componentType": 5125, "count": 768, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1612248, "componentType": 5126, "count": 314, "max": [1.3437399864196777, -3.2401199340820312, -0.25918999314308167], "min": [-1.3437399864196777, -3.6690900325775146, -0.41152000427246094], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1616016, "componentType": 5126, "count": 314, "max": [0.9999071955680847, 0.36251136660575867, 0.9984806180000305], "min": [-0.9999071955680847, -0.9602419137954712, -0.9998132586479187], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1316184, "componentType": 5125, "count": 1680, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1619784, "componentType": 5126, "count": 11725, "max": [1.558150053024292, 3.7604498863220215, 0.19870999455451965], "min": [-1.558150053024292, 2.3615100383758545, -0.7823500037193298], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1760484, "componentType": 5126, "count": 11725, "max": [0.9996895790100098, 0.9999894499778748, 0.9999383687973022], "min": [-0.9996899366378784, -0.9789692759513855, -0.9988710284233093], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 362584, "componentType": 5126, "count": 11725, "max": [2.335520029067993, 2.292759895324707], "min": [-1.7469899654388428, -1.11312997341156], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1322904, "componentType": 5125, "count": 60960, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1901184, "componentType": 5126, "count": 27777, "max": [1.5598000288009644, 3.589560031890869, 1.0265899896621704], "min": [-1.5598000288009644, -1.4598699808120728, -0.8300600051879883], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2234508, "componentType": 5126, "count": 27777, "max": [0.9999995827674866, 0.9941354393959045, 0.9995213150978088], "min": [-0.9999995827674866, -0.9999882578849792, -0.9998671412467957], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 456384, "componentType": 5126, "count": 27777, "max": [2.678339958190918, 2.6552600860595703], "min": [-1.7507100105285645, -1.6289299726486206], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1566744, "componentType": 5125, "count": 144192, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2567832, "componentType": 5126, "count": 4836, "max": [1.3735599517822266, 0.08854000270366669, 0.12204000353813171], "min": [-1.3735599517822266, -0.6015400290489197, -0.5473700165748596], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2625864, "componentType": 5126, "count": 4836, "max": [0.9663828015327454, 0.9992283582687378, 0.9985776543617249], "min": [-0.9663828015327454, -0.299054890871048, -0.9802151918411255], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 678600, "componentType": 5126, "count": 4836, "max": [0.963670015335083, 0.963729977607727], "min": [0.0002800000074785203, 0.354420006275177], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2143512, "componentType": 5125, "count": 26496, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2683896, "componentType": 5126, "count": 297, "max": [0.6021900177001953, 3.711639881134033, 0.20173999667167664], "min": [-0.6021900177001953, 3.5712499618530273, 0.0451899990439415], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2687460, "componentType": 5126, "count": 297, "max": [0.19492602348327637, 0.8726696968078613, 0.6350105404853821], "min": [-0.19492602348327637, 0.7484824061393738, 0.4883109927177429], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 717288, "componentType": 5126, "count": 297, "max": [0.9223399758338928, 0.5771600008010864], "min": [0.09956000000238419, 0.4542500078678131], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2249496, "componentType": 5125, "count": 1536, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2691024, "componentType": 5126, "count": 5868, "max": [1.272760033607483, 1.9739700555801392, 0.9218500256538391], "min": [-1.272760033607483, -0.5832899808883667, 0.4026600122451782], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2761440, "componentType": 5126, "count": 5868, "max": [0.9937998652458191, 0.9997217059135437, 0.9999619126319885], "min": [-0.9937999844551086, -0.9999517798423767, -1.0], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 2255640, "componentType": 5125, "count": 34176, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 2831856, "componentType": 5126, "count": 17620, "max": [1.634310007095337, -1.5346200466156006, -0.0852700024843216], "min": [-1.648169994354248, -2.6523900032043457, -0.9937899708747864], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3043296, "componentType": 5126, "count": 17620, "max": [0.9999832510948181, 0.9995227456092834, 1.0], "min": [-0.9999372959136963, -0.9995184540748596, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 719664, "componentType": 5126, "count": 17620, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2392344, "componentType": 5125, "count": 55824, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3254736, "componentType": 5126, "count": 10570, "max": [1.6238900423049927, -1.5607399940490723, -0.10567999631166458], "min": [-1.6377500295639038, -2.626270055770874, -0.9733700156211853], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3381576, "componentType": 5126, "count": 10570, "max": [0.9999258518218994, 0.9997291564941406, 1.0], "min": [-0.999947726726532, -0.9997909665107727, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 860624, "componentType": 5126, "count": 10570, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2615640, "componentType": 5125, "count": 59280, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3508416, "componentType": 5126, "count": 4222, "max": [1.6635899543762207, -1.4450500011444092, 0.027969999238848686], "min": [-1.677459955215454, -2.741960048675537, -1.1070300340652466], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3559080, "componentType": 5126, "count": 4222, "max": [0.9998069405555725, 0.9994438290596008, 0.9993695616722107], "min": [-0.9998069405555725, -0.9994428753852844, -0.9993686079978943], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 67552, "componentType": 5126, "count": 4222, "max": [0.9999950528144836, 0.9999545812606812, 0.999374508857727, 1.0], "min": [-0.9266493320465088, -0.999880313873291, -0.9993755221366882, -1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 945184, "componentType": 5126, "count": 4222, "max": [7.409379959106445, 0.9987800121307373], "min": [-4.900440216064453, 0.0232900008559227], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 978960, "componentType": 5126, "count": 4222, "max": [7.409379959106445, 0.9987800121307373], "min": [-4.900440216064453, 0.0232900008559227], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2852760, "componentType": 5125, "count": 23040, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3609744, "componentType": 5126, "count": 3754, "max": [1.2717100381851196, -2.161870002746582, -0.24011999368667603], "min": [-1.4394700527191162, -2.502929925918579, -0.8765599727630615], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3654792, "componentType": 5126, "count": 3754, "max": [0.9923980236053467, 0.9989617466926575, 1.0], "min": [-0.9990028738975525, -0.9991167783737183, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1012736, "componentType": 5126, "count": 3754, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2944920, "componentType": 5125, "count": 19968, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3699840, "componentType": 5126, "count": 24170, "max": [-0.7554200291633606, -0.9185100197792053, 0.40582001209259033], "min": [-1.5697499513626099, -3.20100998878479, -0.628059983253479], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3989880, "componentType": 5126, "count": 24170, "max": [0.9999569058418274, 0.9202430248260498, 0.9999944567680359], "min": [-0.999999463558197, -0.9994412064552307, -0.9999807476997375], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1042768, "componentType": 5126, "count": 24170, "max": [2.1860198974609375, 2.2268900871276855], "min": [-1.3405300378799438, -1.2571300268173218], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3024792, "componentType": 5125, "count": 134784, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4279920, "componentType": 5126, "count": 3208, "max": [1.2400399446487427, 1.9869600534439087, 0.8946400284767151], "min": [-1.2400399446487427, 0.7988899946212769, 0.41328001022338867], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4318416, "componentType": 5126, "count": 3208, "max": [0.9998549818992615, 0.5316164493560791, 0.7449445724487305], "min": [-0.9998549818992615, -0.5616462230682373, -0.5360319018363953], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 3563928, "componentType": 5125, "count": 18432, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4356912, "componentType": 5126, "count": 180, "max": [0.8535900115966797, 2.8453800678253174, 0.9040799736976624], "min": [-0.8535900115966797, 1.6919699907302856, 0.5188300013542175], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4359072, "componentType": 5126, "count": 180, "max": [0.280530720949173, 0.3698301911354065, 0.9710734486579895], "min": [-0.280530720949173, 0.23597709834575653, 0.9212149977684021], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 3637656, "componentType": 5125, "count": 720, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4361232, "componentType": 5126, "count": 6, "max": [0.06296999752521515, -3.318959951400757, -0.009429999627172947], "min": [-0.06296999752521515, -3.450119972229004, -0.0741100013256073], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4361304, "componentType": 5126, "count": 6, "max": [0.0265502892434597, -0.4413347840309143, 0.8972650170326233], "min": [-0.0265502892434597, -0.44149231910705566, 0.8969497084617615], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1236128, "componentType": 5126, "count": 6, "max": [0.8403000235557556, 0.902899980545044], "min": [0.15970000624656677, 0.22231000661849976], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3640536, "componentType": 5125, "count": 12, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4361376, "componentType": 5126, "count": 8, "max": [1.0832500457763672, 0.01432000007480383, 0.2935999929904938], "min": [-1.0832500457763672, 0.004490000195801258, -0.27081000804901123], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4361472, "componentType": 5126, "count": 8, "max": [0.013610373251140118, 0.9999775290489197, 0.0], "min": [-0.013610373251140118, 0.9999074339866638, 0.0], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 135104, "componentType": 5126, "count": 8, "max": [-0.9994099140167236, 0.013603604398667812, 0.03153945878148079, 1.0], "min": [-0.9999222755432129, -0.01360360812395811, -0.03153255954384804, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1236176, "componentType": 5126, "count": 8, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3640584, "componentType": 5125, "count": 18, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4361568, "componentType": 5126, "count": 10, "max": [1.0832500457763672, 0.027780000120401382, 0.27081000804901123], "min": [-1.0832500457763672, 0.0, -0.27081000804901123], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4361688, "componentType": 5126, "count": 10, "max": [0.033390749245882034, 1.0, 0.0], "min": [-0.033390749245882034, 0.9994423985481262, 0.0], "type": "VEC3"}, {"bufferView": 3, "byteOffset": 135232, "componentType": 5126, "count": 10, "max": [-0.9994423985481262, 0.033390749245882034, 0.0, 1.0], "min": [-1.0, -0.033390749245882034, 0.0, 1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 1236240, "componentType": 5126, "count": 10, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3640656, "componentType": 5125, "count": 24, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4361808, "componentType": 5126, "count": 57129, "max": [1.7078399658203125, 3.363759994506836, 1.0931700468063354], "min": [-1.7078399658203125, -3.7570300102233887, -0.8587999939918518], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5047356, "componentType": 5126, "count": 57129, "max": [1.0, 0.9999967217445374, 0.9999998211860657], "min": [-0.9999991059303284, -0.9999586939811707, -0.9999966025352478], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1236320, "componentType": 5126, "count": 57129, "max": [2.609499931335449, 2.719980001449585], "min": [-1.685979962348938, -1.503059983253479], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3640752, "componentType": 5125, "count": 295440, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5732904, "componentType": 5126, "count": 11160, "max": [1.6887199878692627, 3.723720073699951, 0.9523400068283081], "min": [-1.6887199878692627, -3.2850399017333984, -0.7167699933052063], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5866824, "componentType": 5126, "count": 11160, "max": [1.0, 0.999701201915741, 1.0], "min": [-1.0, -0.9993752837181091, -1.0], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 4822512, "componentType": 5125, "count": 63168, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6000744, "componentType": 5126, "count": 26322, "max": [1.6999800205230713, 3.7572898864746094, 0.558489978313446], "min": [-1.6999800205230713, -3.810580015182495, -0.8304399847984314], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6316608, "componentType": 5126, "count": 26322, "max": [1.0, 0.9995028972625732, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 5075184, "componentType": 5125, "count": 152688, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6632472, "componentType": 5126, "count": 65532, "max": [1.7127399444580078, 3.765350103378296, 1.0315200090408325], "min": [-1.5647000074386597, -3.761929988861084, -0.8636999726295471], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7418856, "componentType": 5126, "count": 65532, "max": [1.0, 0.9999967217445374, 0.9999995827674866], "min": [-0.9999988675117493, -0.999987781047821, -0.9999964833259583], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 5685936, "componentType": 5125, "count": 376935, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8205240, "componentType": 5126, "count": 27689, "max": [1.7036900520324707, 3.7642199993133545, 1.0980700254440308], "min": [-1.7127399444580078, -3.761630058288574, -0.8316900134086609], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8537508, "componentType": 5126, "count": 27689, "max": [0.9999991059303284, 0.9998397827148438, 0.9999760985374451], "min": [-1.0, -0.9998162388801575, -0.9999547600746155], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 7193676, "componentType": 5125, "count": 155913, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8869776, "componentType": 5126, "count": 2432, "max": [2.6332499980926514, 3.221440076828003, 2.3024001121520996], "min": [-2.6332499980926514, 2.073040008544922, 0.6252099871635437], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8898960, "componentType": 5126, "count": 2432, "max": [0.9606409072875977, 0.9996861815452576, 0.9976004958152771], "min": [-0.9606409072875977, -0.9534467458724976, -0.08842960000038147], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 7817328, "componentType": 5125, "count": 3648, "type": "SCALAR"}], "asset": {"extras": {"author": "<PERSON><PERSON> (https://sketchfab.com/karol<PERSON><PERSON>)", "license": "CC-BY-SA-4.0 (http://creativecommons.org/licenses/by-sa/4.0/)", "source": "https://sketchfab.com/3d-models/free-porsche-911-carrera-4s-d01b254483794de3819786d93e0e1ebf", "title": "(FREE) Porsche 911 Carrera 4S"}, "generator": "Sketchfab-12.65.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 7831920, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 1693352, "byteOffset": 7831920, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 8928144, "byteOffset": 9525272, "byteStride": 12, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 135392, "byteOffset": 18453416, "byteStride": 16, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 18588808, "uri": "scene.bin"}], "extensionsUsed": ["KHR_materials_clearcoat", "KHR_materials_transmission"], "images": [{"uri": "textures/paint_metallicRoughness.png"}, {"uri": "textures/license_baseColor.png"}, {"uri": "textures/license_normal.png"}, {"uri": "textures/logo_baseColor.png"}, {"uri": "textures/rubber_metallicRoughness.png"}, {"uri": "textures/rubber_normal.png"}, {"uri": "textures/tex_shiny_baseColor.png"}, {"uri": "textures/tex_shiny_emissive.png"}, {"uri": "textures/Material_baseColor.png"}, {"uri": "textures/Material_metallicRoughness.png"}], "materials": [{"doubleSided": true, "name": "full_black", "pbrMetallicRoughness": {"baseColorFactor": [0.008228968597748665, 0.008228968597748665, 0.008228968597748665, 1.0]}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "coat", "pbrMetallicRoughness": {"baseColorFactor": [0.15040833213407032, 0.15040833213407032, 0.15040833213407032, 0.45772303595206393], "roughnessFactor": 0.03819108768217719}}, {"doubleSided": true, "name": "plastic", "pbrMetallicRoughness": {"baseColorFactor": [0.009583863431596533, 0.009583863431596533, 0.009583863431596533, 1.0], "metallicFactor": 0.38476095799208365, "roughnessFactor": 0.5185247675853808}}, {"doubleSided": true, "extensions": {"KHR_materials_clearcoat": {"clearcoatFactor": 1.0, "clearcoatRoughnessFactor": 0.04}}, "name": "silver", "pbrMetallicRoughness": {"baseColorFactor": [0.5035857317316067, 0.5035857317316067, 0.5035857317316067, 1.0], "roughnessFactor": 0.2625889985346782}}, {"doubleSided": true, "name": "paint", "pbrMetallicRoughness": {"baseColorFactor": [0.3887443508488501, 0.3887443508488501, 0.3887443508488501, 1.0], "metallicRoughnessTexture": {"index": 0}}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "license", "normalTexture": {"index": 2, "scale": 0.23275662890879134}, "pbrMetallicRoughness": {"baseColorFactor": [0.6583687503420098, 0.6583687503420098, 0.6583687503420098, 1.0], "baseColorTexture": {"index": 1}, "metallicFactor": 0.3421997458487618, "roughnessFactor": 0.14098553526804436}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "logo", "pbrMetallicRoughness": {"baseColorTexture": {"index": 3}, "metallicFactor": 0.8894153305486141, "roughnessFactor": 0.3361195726854301}}, {"doubleSided": true, "name": "rubber", "normalTexture": {"index": 5}, "pbrMetallicRoughness": {"baseColorFactor": [0.**********, 0.**********, 0.**********, 1.0], "metallicFactor": 0.45772303595206393, "metallicRoughnessTexture": {"index": 4}}}, {"doubleSided": true, "extensions": {"KHR_materials_clearcoat": {"clearcoatFactor": 1.0, "clearcoatRoughnessFactor": 0.04}}, "name": "Material.001", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 0.5122415946564827, 0.0, 1.0], "metallicFactor": 0.29963853370543997, "roughnessFactor": 0.5732463260553661}}, {"alphaMode": "BLEND", "extensions": {"KHR_materials_clearcoat": {"clearcoatFactor": 1.0, "clearcoatRoughnessFactor": 0.04}}, "name": "window", "pbrMetallicRoughness": {"baseColorFactor": [0.0070474097651222695, 0.016142641006358316, 0.021209138529855905, 0.9441368890185993], "metallicFactor": 0.5870807927, "roughnessFactor": 0.396921304318747}}, {"alphaMode": "BLEND", "doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 7}, "extensions": {"KHR_materials_clearcoat": {"clearcoatFactor": 1.0, "clearcoatRoughnessFactor": 0.0}}, "name": "tex_shiny", "pbrMetallicRoughness": {"baseColorTexture": {"index": 6}, "roughnessFactor": 0.536765287075376}}, {"alphaMode": "BLEND", "doubleSided": true, "extensions": {"KHR_materials_clearcoat": {"clearcoatFactor": 1.0, "clearcoatRoughnessFactor": 0.0}}, "name": "glass", "pbrMetallicRoughness": {"baseColorFactor": [0.0062837212, 0.006819917, 0.0071492178, 0.6141387195], "metallicFactor": 0.2578125, "roughnessFactor": 0.3537915959846536}}, {"alphaMode": "BLEND", "doubleSided": true, "extensions": {"KHR_materials_transmission": {"transmissionFactor": 1.0}}, "name": "lights", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 0.25], "roughnessFactor": 0.0}}, {"alphaMode": "BLEND", "name": "Material", "occlusionTexture": {"index": 9}, "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 1.0], "baseColorTexture": {"index": 8}, "metallicRoughnessTexture": {"index": 9}}}], "meshes": [{"name": "window_rear_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0}, "indices": 2, "material": 9, "mode": 4}]}, {"name": "windshield_0", "primitives": [{"attributes": {"NORMAL": 4, "POSITION": 3}, "indices": 5, "material": 9, "mode": 4}]}, {"name": "windshield_1", "primitives": [{"attributes": {"NORMAL": 7, "POSITION": 6}, "indices": 8, "material": 2, "mode": 4}]}, {"name": "Plane.002_0", "primitives": [{"attributes": {"NORMAL": 10, "POSITION": 9, "TEXCOORD_0": 11}, "indices": 12, "material": 4, "mode": 4}]}, {"name": "Plane.003_0", "primitives": [{"attributes": {"NORMAL": 14, "POSITION": 13, "TEXCOORD_0": 15}, "indices": 16, "material": 4, "mode": 4}]}, {"name": "Plane.004_0", "primitives": [{"attributes": {"NORMAL": 18, "POSITION": 17, "TEXCOORD_0": 19}, "indices": 20, "material": 4, "mode": 4}]}, {"name": "boot_0", "primitives": [{"attributes": {"NORMAL": 22, "POSITION": 21}, "indices": 23, "material": 0, "mode": 4}]}, {"name": "underbody_0", "primitives": [{"attributes": {"NORMAL": 25, "POSITION": 24}, "indices": 26, "material": 0, "mode": 4}]}, {"name": "Cylinder.000_0", "primitives": [{"attributes": {"NORMAL": 28, "POSITION": 27, "TEXCOORD_0": 29}, "indices": 30, "material": 3, "mode": 4}]}, {"name": "Cylinder.000_1", "primitives": [{"attributes": {"NORMAL": 32, "POSITION": 31, "TEXCOORD_0": 33}, "indices": 34, "material": 2, "mode": 4}]}, {"name": "Cylinder.000_2", "primitives": [{"attributes": {"NORMAL": 36, "POSITION": 35, "TANGENT": 37, "TEXCOORD_0": 38, "TEXCOORD_1": 39}, "indices": 40, "material": 7, "mode": 4}]}, {"name": "Cylinder.000_3", "primitives": [{"attributes": {"NORMAL": 42, "POSITION": 41, "TEXCOORD_0": 43}, "indices": 44, "material": 8, "mode": 4}]}, {"name": "Plane_0", "primitives": [{"attributes": {"NORMAL": 46, "POSITION": 45, "TEXCOORD_0": 47}, "indices": 48, "material": 13, "mode": 4}]}, {"name": "Cube.001_0", "primitives": [{"attributes": {"NORMAL": 50, "POSITION": 49}, "indices": 51, "material": 2, "mode": 4}]}, {"name": "bumper_front.004_0", "primitives": [{"attributes": {"NORMAL": 53, "POSITION": 52}, "indices": 54, "material": 3, "mode": 4}]}, {"name": "bumper_front.004_1", "primitives": [{"attributes": {"NORMAL": 56, "POSITION": 55}, "indices": 57, "material": 12, "mode": 4}]}, {"name": "bumper_front.004_2", "primitives": [{"attributes": {"NORMAL": 59, "POSITION": 58}, "indices": 60, "material": 2, "mode": 4}]}, {"name": "bumper_front.007_0", "primitives": [{"attributes": {"NORMAL": 62, "POSITION": 61}, "indices": 63, "material": 11, "mode": 4}]}, {"name": "bumper_front.009_0", "primitives": [{"attributes": {"NORMAL": 65, "POSITION": 64, "TEXCOORD_0": 66}, "indices": 67, "material": 10, "mode": 4}]}, {"name": "bumper_front.001_0", "primitives": [{"attributes": {"NORMAL": 69, "POSITION": 68}, "indices": 70, "material": 2, "mode": 4}]}, {"name": "bumper_front.001_1", "primitives": [{"attributes": {"NORMAL": 72, "POSITION": 71}, "indices": 73, "material": 3, "mode": 4}]}, {"name": "bumper_front.001_2", "primitives": [{"attributes": {"NORMAL": 75, "POSITION": 74}, "indices": 76, "material": 12, "mode": 4}]}, {"name": "bumper_front.003_0", "primitives": [{"attributes": {"NORMAL": 78, "POSITION": 77}, "indices": 79, "material": 2, "mode": 4}]}, {"name": "bumper_front.003_1", "primitives": [{"attributes": {"NORMAL": 81, "POSITION": 80}, "indices": 82, "material": 11, "mode": 4}]}, {"name": "boot.001_0", "primitives": [{"attributes": {"NORMAL": 84, "POSITION": 83, "TEXCOORD_0": 85}, "indices": 86, "material": 4, "mode": 4}]}, {"name": "boot.002_0", "primitives": [{"attributes": {"NORMAL": 88, "POSITION": 87, "TEXCOORD_0": 89}, "indices": 90, "material": 4, "mode": 4}]}, {"name": "Plane.001_0", "primitives": [{"attributes": {"NORMAL": 92, "POSITION": 91, "TEXCOORD_0": 93}, "indices": 94, "material": 10, "mode": 4}]}, {"name": "boot.003_0", "primitives": [{"attributes": {"NORMAL": 96, "POSITION": 95, "TEXCOORD_0": 97}, "indices": 98, "material": 10, "mode": 4}]}, {"name": "boot.004_0", "primitives": [{"attributes": {"NORMAL": 100, "POSITION": 99}, "indices": 101, "material": 9, "mode": 4}]}, {"name": "Cylinder.001_0", "primitives": [{"attributes": {"NORMAL": 103, "POSITION": 102, "TEXCOORD_0": 104}, "indices": 105, "material": 3, "mode": 4}]}, {"name": "Cylinder.001_1", "primitives": [{"attributes": {"NORMAL": 107, "POSITION": 106, "TEXCOORD_0": 108}, "indices": 109, "material": 2, "mode": 4}]}, {"name": "Cylinder.001_2", "primitives": [{"attributes": {"NORMAL": 111, "POSITION": 110, "TANGENT": 112, "TEXCOORD_0": 113, "TEXCOORD_1": 114}, "indices": 115, "material": 7, "mode": 4}]}, {"name": "Cylinder.001_3", "primitives": [{"attributes": {"NORMAL": 117, "POSITION": 116, "TEXCOORD_0": 118}, "indices": 119, "material": 8, "mode": 4}]}, {"name": "boot.005_0", "primitives": [{"attributes": {"NORMAL": 121, "POSITION": 120, "TEXCOORD_0": 122}, "indices": 123, "material": 4, "mode": 4}]}, {"name": "boot.006_0", "primitives": [{"attributes": {"NORMAL": 125, "POSITION": 124}, "indices": 126, "material": 0, "mode": 4}]}, {"name": "window_rear.001_0", "primitives": [{"attributes": {"NORMAL": 128, "POSITION": 127}, "indices": 129, "material": 0, "mode": 4}]}, {"name": "boot.007_0", "primitives": [{"attributes": {"NORMAL": 131, "POSITION": 130, "TEXCOORD_0": 132}, "indices": 133, "material": 6, "mode": 4}]}, {"name": "Plane.005_0", "primitives": [{"attributes": {"NORMAL": 135, "POSITION": 134, "TANGENT": 136, "TEXCOORD_0": 137}, "indices": 138, "material": 5, "mode": 4}]}, {"name": "Plane.006_0", "primitives": [{"attributes": {"NORMAL": 140, "POSITION": 139, "TANGENT": 141, "TEXCOORD_0": 142}, "indices": 143, "material": 5, "mode": 4}]}, {"name": "boot.008_0", "primitives": [{"attributes": {"NORMAL": 145, "POSITION": 144, "TEXCOORD_0": 146}, "indices": 147, "material": 4, "mode": 4}]}, {"name": "boot.009_0", "primitives": [{"attributes": {"NORMAL": 149, "POSITION": 148}, "indices": 150, "material": 3, "mode": 4}]}, {"name": "boot.010_0", "primitives": [{"attributes": {"NORMAL": 152, "POSITION": 151}, "indices": 153, "material": 2, "mode": 4}]}, {"name": "boot.011_0", "primitives": [{"attributes": {"NORMAL": 155, "POSITION": 154}, "indices": 156, "material": 1, "mode": 4}]}, {"name": "boot.011_0", "primitives": [{"attributes": {"NORMAL": 158, "POSITION": 157}, "indices": 159, "material": 1, "mode": 4}]}, {"name": "Cube.002_0", "primitives": [{"attributes": {"NORMAL": 161, "POSITION": 160}, "indices": 162, "material": 0, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [0.5784255266189575, 0.0, 0.0, 0.0, 0.0, 1.284362675366596e-16, -0.5784255266189575, 0.0, 0.0, 0.5784255266189575, 1.284362675366596e-16, 0.0, -0.015285167843103409, -0.00915591418743135, 0.06289219111204147, 1.0], "name": "Sketchfab_model"}, {"children": [2, 4, 7, 9, 11, 13, 15, 17, 22, 24, 26, 30, 32, 34, 38, 41, 43, 45, 47, 49, 51, 56, 58, 60, 62, 64, 66, 68, 70, 72, 74, 76, 78, 81], "name": "Root"}, {"children": [3], "name": "window_rear"}, {"mesh": 0, "name": "window_rear_0"}, {"children": [5, 6], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, -0.003190000000000001, 0.007190000000000002, 1.0], "name": "windshield"}, {"mesh": 1, "name": "windshield_0"}, {"mesh": 2, "name": "windshield_1"}, {"children": [8], "matrix": [0.014200000000000003, 0.012290000000000002, -0.015620000000000002, 0.0, -0.013920000000000002, -0.007550000000000002, -0.018600000000000002, 0.0, -0.014190000000000001, 0.019710000000000002, 0.0026100000000000008, 0.0, -1.05305, 3.5102499999999996, -0.1259, 1.0], "name": "Plane.002"}, {"mesh": 3, "name": "Plane.002_0"}, {"children": [10], "matrix": [0.016870000000000003, -0.00023000000000000006, -0.017660000000000006, 0.0, -0.017480000000000006, 0.003320000000000001, -0.01674, 0.0, 0.0025600000000000006, 0.024200000000000003, 0.0021200000000000004, 0.0, 0.43627000000000005, 3.7233500000000004, -0.11696000000000002, 1.0], "name": "Plane.003"}, {"mesh": 4, "name": "Plane.003_0"}, {"children": [12], "matrix": [0.04105000000000001, 0.00841, -0.04172000000000001, 0.0, -0.042480000000000004, 0.004490000000000001, -0.04089000000000001, 0.0, -0.002650000000000001, 0.05836000000000001, 0.009160000000000001, 0.0, -0.48751000000000005, 3.6843700000000004, -0.32849000000000006, 1.0], "name": "Plane.004"}, {"mesh": 5, "name": "Plane.004_0"}, {"children": [14], "name": "boot"}, {"mesh": 6, "name": "boot_0"}, {"children": [16], "name": "underbody"}, {"mesh": 7, "name": "underbody_0"}, {"children": [18, 19, 20, 21], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, -0.0, 0.029350000000000008, 1.0], "name": "Cylinder.000"}, {"mesh": 8, "name": "Cylinder.000_0"}, {"mesh": 9, "name": "Cylinder.000_1"}, {"mesh": 10, "name": "Cylinder.000_2"}, {"mesh": 11, "name": "Cylinder.000_3"}, {"children": [23], "matrix": [6.9534, 0.0, 0.0, 0.0, 0.0, 9.78514, 0.0, 0.0, 0.0, 0.0, 7.495979999999999, 0.0, 0.0, 0.0, -1.05402, 1.0], "name": "Plane"}, {"mesh": 12, "name": "Plane_0"}, {"children": [25], "matrix": [0.013800000000000003, -0.003260000000000001, -0.0015100000000000005, 0.0, 0.0034500000000000012, 0.010340000000000002, 0.009190000000000002, 0.0, -0.0008400000000000003, -0.007730000000000002, 0.009010000000000002, 0.0, 0.035820000000000005, -1.56003, 0.33303000000000005, 1.0], "name": "Cube.001"}, {"mesh": 13, "name": "Cube.001_0"}, {"children": [27, 28, 29], "name": "bumper_front.004"}, {"mesh": 14, "name": "bumper_front.004_0"}, {"mesh": 15, "name": "bumper_front.004_1"}, {"mesh": 16, "name": "bumper_front.004_2"}, {"children": [31], "matrix": [1.0357399999999999, 0.0, 0.0, 0.0, 0.0, 1.0357199999999998, -0.006270000000000001, 0.0, 0.0, 0.006270000000000001, 1.0357199999999998, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "bumper_front.007"}, {"mesh": 17, "name": "bumper_front.007_0"}, {"children": [33], "name": "bumper_front.009"}, {"mesh": 18, "name": "bumper_front.009_0"}, {"children": [35, 36, 37], "name": "bumper_front.001"}, {"mesh": 19, "name": "bumper_front.001_0"}, {"mesh": 20, "name": "bumper_front.001_1"}, {"mesh": 21, "name": "bumper_front.001_2"}, {"children": [39, 40], "name": "bumper_front.003"}, {"mesh": 22, "name": "bumper_front.003_0"}, {"mesh": 23, "name": "bumper_front.003_1"}, {"children": [42], "name": "boot.001"}, {"mesh": 24, "name": "boot.001_0"}, {"children": [44], "name": "boot.002"}, {"mesh": 25, "name": "boot.002_0"}, {"children": [46], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.005350000000000001, 3.58116, 0.10747000000000001, 1.0], "name": "Plane.001"}, {"mesh": 26, "name": "Plane.001_0"}, {"children": [48], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.002640000000000001, 0.0, 1.0], "name": "boot.003"}, {"mesh": 27, "name": "boot.003_0"}, {"children": [50], "name": "boot.004"}, {"mesh": 28, "name": "boot.004_0"}, {"children": [52, 53, 54, 55], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, -0.0, 0.029350000000000008, 1.0], "name": "Cylinder.001"}, {"mesh": 29, "name": "Cylinder.001_0"}, {"mesh": 30, "name": "Cylinder.001_1"}, {"mesh": 31, "name": "Cylinder.001_2"}, {"mesh": 32, "name": "Cylinder.001_3"}, {"children": [57], "name": "boot.005"}, {"mesh": 33, "name": "boot.005_0"}, {"children": [59], "name": "boot.006"}, {"mesh": 34, "name": "boot.006_0"}, {"children": [61], "name": "window_rear.001"}, {"mesh": 35, "name": "window_rear.001_0"}, {"children": [63], "matrix": [0.99122, -0.08178000000000002, 0.10386000000000001, 0.0, 0.006740000000000002, 0.81591, 0.57815, 0.0, -0.13202, -0.57237, 0.8093, 0.0, -1.9993899999999998, -4.411790000000001, 4.467709999999999, 1.0], "name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"children": [65], "matrix": [0.12355000000000001, -0.7309800000000001, -0.6711200000000002, 0.0, -0.30774, 0.6147400000000002, -0.7262200000000001, 0.0, 0.94342, 0.29625, -0.14900000000000002, 0.0, 1.4270299999999998, 0.23691000000000004, 2.62923, 1.0], "name": "Hemi.001"}, {"name": "Hemi.001"}, {"children": [67], "name": "boot.007"}, {"mesh": 36, "name": "boot.007_0"}, {"children": [69], "matrix": [0.3933400000000001, 0.0, -0.0, 0.0, 0.0, 0.3908000000000001, 0.04466000000000001, 0.0, 0.0, -0.040380000000000006, 0.35331000000000007, 0.0, 0.0, 3.7042800000000002, -0.29221, 1.0], "name": "Plane.005"}, {"mesh": 37, "name": "Plane.005_0"}, {"children": [71], "matrix": [-0.39521000000000006, 0.0, 0.0, 0.0, -0.0, -0.3938900000000001, -0.032220000000000006, 0.0, -0.0, -0.029130000000000003, 0.35611000000000004, 0.0, -0.0, -3.7503300000000004, -0.43239000000000005, 1.0], "name": "Plane.006"}, {"mesh": 38, "name": "Plane.006_0"}, {"children": [73], "name": "boot.008"}, {"mesh": 39, "name": "boot.008_0"}, {"children": [75], "name": "boot.009"}, {"mesh": 40, "name": "boot.009_0"}, {"children": [77], "name": "boot.010"}, {"mesh": 41, "name": "boot.010_0"}, {"children": [79, 80], "name": "boot.011"}, {"mesh": 42, "name": "boot.011_0"}, {"mesh": 43, "name": "boot.011_0"}, {"children": [82], "matrix": [0.3323200000000001, 0.0, 0.0, 0.0, 0.0, 0.3178700000000001, 0.0, 0.0, 0.0, 0.0, 0.3178700000000001, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Cube.002"}, {"mesh": 44, "name": "Cube.002_0"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 1}, {"sampler": 0, "source": 2}, {"sampler": 0, "source": 3}, {"sampler": 0, "source": 4}, {"sampler": 0, "source": 5}, {"sampler": 0, "source": 6}, {"sampler": 0, "source": 7}, {"sampler": 0, "source": 8}, {"sampler": 0, "source": 9}]}