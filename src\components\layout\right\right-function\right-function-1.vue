<template>
  <!--
   1．监测每个探测器（手报、双鉴）的报警/报警恢复信息。
  2．监测防盗报警系统的撤布防状态。
  3．控制防盗报警系统的布防、撤防。
  -->
  <!--  <div class="w-[100%] h-[220px]">
      <use-charts :options="option" class="h-full w-full"> </use-charts>
    </div>-->
  <a-scrollbar class="w-full max-h-[18vh] overflow-y-auto">
    <table>
      <thead>
        <tr>
          <th>警情事件</th>
          <th>事件状态</th>
          <th style="width: 70px">设备编号</th>
          <th style="width: 70px">防区编号</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(item, index) in translatedData"
          :key="index"
          :class="{ 'odd-row': index % 2 === 0, 'even-row': index % 2 !== 0 }"
        >
          <td>{{ item.eventName }}</td>
          <td>{{ item.eventVal }}</td>
          <td>{{ item.devNo }}</td>
          <td>{{ item.zoneNo }}</td>
        </tr>
      </tbody>
    </table>
  </a-scrollbar>
</template>

<script setup name="FunctionRight1">
  /*import UseCharts from "@/common/useCharts.vue";
const option = {
  color: ["#D53A35", "#E98F6F", "#6AB0B8", "#334B5C"],
  //title: {
  //    text: '报警次数'
  //},
  tooltip: {
    trigger: "axis",
    //formatter: "{b} <br> 合格率: {c}%"
  },
  legend: {
    data: ["手动报警", "双鉴报警"],
    icon: "circle",
    top: "0%",
    textStyle: {
      color: "#ffffff",
    },
  },
  grid: {
    left: "3%",
    top: "14%",
    right: "4%",
    bottom: "30%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    name: "日期",
    boundaryGap: false,
    data: ["5.1", "5.2", "5.3", "5.4", "5.5", "5.6", "5.7"],
    axisLabel: {
      interval: 0,
      textStyle: {
        color: "#fff",
      },
      // 默认x轴字体大小
      fontSize: 12,
      // margin:文字到x轴的距离
      margin: 5,
    },
  },
  yAxis: {
    type: "value",
    name: "报警次数",
    nameTextStyle: {
      color: "#ffffff",
    },
    axisLabel: {
      textStyle: {
        color: "#fff",
        // 默认x轴字体大小
        fontSize: 12,
        // margin:文字到x轴的距离
        margin: 5,
      },
    },
  },
  series: [
    {
      name: "手动报警",
      type: "line",
      stack: "总量",
      data: [0, 1, 0, 1, 2, 0, 1],
    },
    {
      name: "双鉴报警",
      type: "line",
      stack: "总量",
      data: [5, 4, 4, 2, 6, 4, 10],
    },
  ],
};*/

  import request from '@/utils/request.js'
  import aScrollbar from '@/common/scrollbar.vue'

  const translatedData = ref([])

  const translateEvent = (event) => {
    const eventTranslations = {
      Arm: {
        name: '布撤防状态',
        values: {
          0: '撤防',
          1: '布防',
          2: '留守布防',
          3: '高压布防',
          4: '低压布防',
        },
      },
      Alarm: {
        name: '报警状态',
        values: {
          0: '防区报警恢复',
          1: '防区报警',
        },
      },
      DevOnline: {
        name: '设备在线状态',
        values: {
          0: '掉线',
          1: '在线',
        },
      },
      DevTamper: {
        name: '设备被撬状态',
        values: {
          0: '被撬恢复',
          1: '被撬',
        },
      },
      DevDcLow: {
        name: '设备欠压',
        values: {
          0: '欠压恢复',
          1: '欠压',
        },
      },
      DevAcLoss: {
        name: '交流断电',
        values: {
          0: '交流断电恢复',
          1: '交流断电',
        },
      },
      FenceAlarm: {
        name: '围栏防区报警状态',
        values: {
          0: '恢复',
          1: '短路报警',
          2: '断路报警',
          3: '触网报警',
        },
      },
      FenceSlack: {
        name: '围栏线松弛报警',
        values: {
          0: '正常',
          1: '线松弛',
        },
      },
      FenceCut: {
        name: '围栏防剪触发报警',
        values: {
          0: '正常',
          1: '防剪触发',
        },
      },
      FenceDcVal: {
        name: '围栏电压值',
        values: (val) => `${val}V`,
      },
      TempVal: {
        name: '温度值',
        values: (val) => `${parseFloat(val).toFixed(1)}℃`,
      },
    }

    return {
      eventName: eventTranslations[event.eventName].name,
      eventVal:
        typeof eventTranslations[event.eventName].values === 'function'
          ? eventTranslations[event.eventName].values(event.eventVal)
          : eventTranslations[event.eventName].values[event.eventVal],
      devNo: event.devNo,
      zoneNo: event.zoneNo,
    }
  }
  onMounted(() => {
    request({
      url: '/hardware/intrusion', //视频切换
      method: 'get',
    }).then((request) => {
      let Data = request.data.map(translateEvent)
      translatedData.value.length = 0
      translatedData.value.push(...Data)
    })
  })
</script>
<style scoped>
  table {
    width: 100%;
    border-collapse: collapse;
    color: #fff;
  }

  th {
    background-color: #1f4170; /* 深蓝色 */
    color: white;
    padding: 6px;
  }

  td {
    padding: 0.3vw;
  }

  tbody tr.odd-row {
    background-color: transparent;
  }

  tbody tr.even-row {
    background-color: #5a7ba3; /* 浅蓝色 */
  }
</style>
