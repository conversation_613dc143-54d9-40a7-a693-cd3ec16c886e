<template>
  <a-scrollbar class="w-full max-h-[18vh] overflow-y-auto">
    <table>
      <thead>
        <tr>
          <th>文件名称</th>
          <th>节目类型</th>
          <th>节目大小</th>
          <th>播放次数</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(item, index) in translatedData"
          :key="index"
          :class="{ 'odd-row': index % 2 === 0, 'even-row': index % 2 !== 0 }"
        >
          <td>{{ item.文件名称 }}</td>
          <td>{{ item.节目类型 }}</td>
          <td>{{ item.节目大小 }}</td>
          <td>{{ item.播放次数 }}</td>
        </tr>
      </tbody>
    </table>
  </a-scrollbar>
</template>
<script setup name="FunctionLeft1">
  import aScrollbar from '@/common/scrollbar.vue'
  import request from '@/utils/request.js'

  const translatedData = ref([])

  const eventTranslations = {
    bdate: {
      name: '创建节目的时分秒',
      values: (val) => `${val}V`,
    },
    fileName: {
      name: '文件名称',
      values: (val) => `${val}V`,
    },
    id: {
      name: '节目编号',
      values: (val) => `${val}V`,
    },
    itemLeng: {
      name: '节目大小',
      values: (val) => `${val}V`,
    },
    itemtype: {
      name: '节目类型',
      values: {
        0: '普通点节目',
        1: '互动节目',
      },
    },
    molType: {
      name: '模版类型',
      values: {
        0: '分辨率',
        1: '固定模版',
      },
    },
    molidname: {
      name: '节目名称',
      values: (val) => `${val}V`,
    },
    ocheck: {
      name: '是否运行查看标识',
      values: (val) => `${val}V`,
    },
    playCount: {
      name: '播放次数',
      values: (val) => `${val}V`,
    },
    state: {
      name: '审核材料',
      values: {
        0: '未审核材料',
        1: '审核通过素材',
        2: '审核失败素材',
      },
    },
    stateUserId: {
      name: '审核人员',
      values: (val) => `${val}V`,
    },
    stime: {
      name: '节目时长',
      values: (val) => `${val}V`,
    },
    userId: {
      name: '用户ID',
      values: (val) => `${val}V`,
    },
    userName: {
      name: '用户名称',
      values: (val) => `${val}V`,
    },
    ydate: {
      name: '创建节目的年月日',
      values: (val) => `${val}V`,
    },
  }
  function translateEventData(data) {
    let translatedData = {}

    for (let key in data) {
      if (eventTranslations[key]) {
        let translation = eventTranslations[key]
        let translatedValue

        if (typeof translation.values === 'function') {
          translatedValue = translation.values(data[key])
        } else if (typeof translation.values === 'object') {
          translatedValue = translation.values[data[key]]
        } else {
          translatedValue = data[key]
        }

        translatedData[translation.name] = translatedValue
      } else {
        translatedData[key] = data[key] // If no translation is found, keep the original key-value pair
      }
    }

    return translatedData
  }
  onMounted(() => {
    request({
      url: '/hardware/info/getItem',
      method: 'get',
    }).then((request) => {
      let Data = request.data.map((item) => translateEventData(item))
      translatedData.value.length = 0
      translatedData.value.push(...Data)
    })
  })
</script>
<style scoped>
  table {
    width: 100%;
    border-collapse: collapse;
    color: #fff;
  }

  th {
    background-color: #1f4170; /* 深蓝色 */
    color: white;
    padding: 6px;
  }

  td {
    padding: 0.3vw;
  }

  tbody tr.odd-row {
    background-color: transparent;
  }

  tbody tr.even-row {
    background-color: #5a7ba3; /* 浅蓝色 */
  }
</style>
