<template>
  <div class="w-[100%] h-[240px]">
    <use-charts :options="option" class="h-full w-full"></use-charts>
  </div>
</template>

<script setup name="FunctionLeft3">
  import UseCharts from '@/common/useCharts.vue'
  import * as echarts from 'echarts'

  const option = {
    backgroundColor: '',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
      },
      formatter: function (params) {
        return `${params[0].name}<br/>
        ${params[0].seriesName}:${params[0].value} 个<br/>
        ${params[1].seriesName}:${params[1].value} 个`
      },
    },
    grid: {
      left: '2%',
      right: '4%',
      bottom: '30%',
      top: '5%',
      containLabel: true,
    },
    legend: {
      data: ['设备正常', '设备异常'],
      right: 10,
      top: 0,
      textStyle: {
        color: '#fff',
      },
      itemWidth: 12,
      itemHeight: 10,
      // itemGap: 35
    },
    xAxis: {
      type: 'category',
      data: ['喷淋头', '烟感', '消防栓', '灭火器', '指示牌', '应急照明灯'],
      axisLine: {
        lineStyle: {
          color: 'white',
        },
      },
      axisLabel: {
        interval: 0,
        // rotate: 40,
        textStyle: {
          fontFamily: 'Microsoft YaHei',
        },
      },
    },

    yAxis: [
      {
        type: 'value',
        name: '单位 个',
        nameTextStyle: {
          color: '#ffffff',
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: true,
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#FFFFFF',
          },
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#ffffff',
          },
        },
      },
    ],
    series: [
      {
        name: '设备正常',
        type: 'bar',
        barWidth: '15%',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#306dc3',
              },
              {
                offset: 1,
                color: '#222c34',
              },
            ]),
            barBorderRadius: 11,
          },
        },
        data: [41, 49, 23, 92, 76, 165, 128],
      },
      {
        name: '设备异常',
        type: 'bar',
        barWidth: '15%',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#ac464b',
              },
              {
                offset: 1,
                color: '#ad686c',
              },
            ]),
            barBorderRadius: 12,
          },
        },

        data: [5, 14, 7, 16, 15, 21, 13],
      },
    ],
  }
</script>

<style scoped></style>
