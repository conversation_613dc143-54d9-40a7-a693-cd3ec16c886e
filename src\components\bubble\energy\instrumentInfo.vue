<template>
  <simple-info :data="data[0]" title="仪表信息" :visible="visible"></simple-info>
</template>

<style scoped></style>
<script setup>
  import request from '@/utils/request.js'
  import SimpleInfo from '@/common/simpleInfo.vue'

  const data = ref([])
  const visible = ref(true)
  const translations = {
    fieldNames: {
      chno: '通道编号',
      sca: '通道倍率',
      ssuid: '仪表ID',
      type: '通道类型',
      val: '通道数值',
    },
    fieldValues: {
      val: {
        0: '关',
        1: '开',
      },
    },
  }

  function translateData(data) {
    return data.map((item) => {
      const translatedItem = {}
      for (const key in item) {
        const translatedKey = translations.fieldNames[key] || key
        const translatedValue = translations.fieldValues[key]
          ? translations.fieldValues[key][item[key]]
          : item[key]

        translatedItem[translatedKey] = translatedValue
      }

      return translatedItem
    })
  }

  onMounted(() => {
    request({
      url: '/hardware/energy/getChdata',
      method: 'get',
    }).then((request) => {
      let result = request.data
      let array = []
      data.value.length = 0
      let resultTranslated = translateData(result)
      const resultData = resultTranslated.map((obj) =>
        Object.entries(obj).map(([key, value]) => ({ name: key, value }))
      )
      data.value.push(...resultData)
    })
  })
</script>
