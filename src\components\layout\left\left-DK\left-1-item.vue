<template>
  <div class="container">
    <div class="left">
      <div class="image-container">
        <img alt="Left Image" src="../../../../assets/images/left-1-bg.png"/>
        <div class="centered-text">{{ title }}</div>
      </div>
    </div>
    <div class="right">
      <div class="top-text">{{ des }}</div>
      <div class="line"></div>
      <div class="bottom-image">
        <div class="bottom-text">{{ number }} <span class="unit">个</span></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "LayoutComponent",
  props: {
    title: {
      type: String,
      required: true,
    },
    des: {
      type: String,
      required: true,
    },
    number: {
      type: Number,
      required: true,
    },
  },
};
</script>

<style scoped>
.container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 205px;
  height: 70px;
  box-sizing: border-box;
}

.left,
.right {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-container {
  position: relative;
  width: 100%;
  max-width: 70px;
}

.image-container img {
  width: 100%;
  height: auto;
}

.centered-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24px;
  text-shadow: 0 0 7px #00a3ff;
}

.right {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.top-text {
  font-weight: 500;
  font-size: 14px;
  color: #e0eeff;
  line-height: 16px;
  text-align: center;
}

.line {
  width: 5vw;
  height: 2px;
  margin-top: 5px;
  margin-bottom: 5px;
  background-image: url("@/assets/images/left-1-line.png");
}

.bottom-image {
  position: relative;
  width: 126px;
  max-width: 300px;
  height: 23px;
  background-image: url("@/assets/images/left-1-bt.png");
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-text {
  font-weight: bold;
  font-size: 20px;
  color: #e3f2ff;
  line-height: 23px;
  text-shadow: 0px 0px 18px rgba(0, 209, 255, 0.4);
  text-align: center;
}

.unit {
  font-weight: 500;
  font-size: 14px;
  color: rgba(204, 230, 255, 0.8);
  line-height: 16px;
  text-align: left;
}
</style>
