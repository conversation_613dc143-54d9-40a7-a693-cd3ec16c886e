/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/18 09时49分40秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 模型加载
 */
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js'
import { useLoading } from 'vue-loading-overlay'
import { DRACOLoader } from 'three/addons/loaders/DRAColoader.js'
import * as THREE from 'three'
import 'vue-loading-overlay/dist/css/index.css'
import { modelRotation } from '@/blueprint/other/modelRotation.js'

// 初始化加载器
const loader = new GLTFLoader()
const dracoLoader = new DRACOLoader()
dracoLoader.setDecoderPath('/draco/')
dracoLoader.setDecoderConfig({ type: 'js' })
loader.setDRACOLoader(dracoLoader)

// 使用Map替代WeakMap，因为我们需要使用URL字符串作为键
const modelCache = new Map()
const $loading = useLoading({
  // options
})

/**
 * 释放模型资源
 * @param {Object} model THREE.js模型对象
 */
function disposeModel(model) {
  if (!model) return;
  
  try {
    if (model.scene) {
      model.scene.traverse((object) => {
        if (object.geometry) {
          object.geometry.dispose();
        }
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => disposeMaterial(material));
          } else {
            disposeMaterial(object.material);
          }
        }
      });
    }

    if (model.animations) {
      model.animations.length = 0;
    }
  } catch (error) {
    console.warn('清理模型资源时发生错误:', error);
  }
}

/**
 * 释放材质及其相关资源
 * @param {THREE.Material} material 
 */
function disposeMaterial(material) {
  if (!material) return;
  
  try {
    // 释放纹理
    if (material.map) material.map.dispose();
    if (material.lightMap) material.lightMap.dispose();
    if (material.bumpMap) material.bumpMap.dispose();
    if (material.normalMap) material.normalMap.dispose();
    if (material.specularMap) material.specularMap.dispose();
    if (material.envMap) material.envMap.dispose();
    
    // 释放材质
    material.dispose();
  } catch (error) {
    console.warn('清理材质资源时发生错误:', error);
  }
}

/**
 * @description 加载通过压缩过的gltf
 * @param url 模型URL
 * @param callback 回调函数
 * @param name 模型名称
 * @param params 参数配置 缩放 位置
 */
async function dracoLoaderModel(url, callback, name = null, params = {}) {
  const loaderCss = $loading.show();
  
  try {
    // 检查缓存
    let gltf = modelCache.get(url);
    
    if (!gltf) {
      // 如果没有缓存，则加载模型
      gltf = await new Promise((resolve, reject) => {
        loader.load(
          url,
          (result) => {
            // 为了避免引用问题，克隆场景和动画
            const clonedResult = {
              scene: result.scene.clone(),
              animations: result.animations.map(anim => anim.clone())
            };
            resolve(clonedResult);
          },
          (xhr) => {
            if (xhr.lengthComputable) {
              const percent = (xhr.loaded / xhr.total) * 100;
              console.log(`Loading model: ${Math.round(percent)}%`);
            }
          },
          reject
        );
      });
      
      // 存入缓存
      modelCache.set(url, gltf);
    }

    // 创建一个新的实例
    const instance = {
      scene: gltf.scene.clone(),
      animations: gltf.animations.map(anim => anim.clone())
    };
    
    // 应用变换
    if (params.scale) {
      instance.scene.scale.set(params.scale.x, params.scale.y, params.scale.z);
    }
    if (params.position) {
      instance.scene.position.set(params.position.x, params.position.y, params.position.z);
    }
    
    // 设置名称
    if (name) {
      instance.scene.traverse(child => {
        child.name = name;
      });
    }

    loaderCss.hide();
    callback(instance);
  } catch (error) {
    loaderCss.hide();
    console.error('加载模型失败:', error);
    throw error;
  }
}

// 程序退出时清理资源
window.addEventListener('unload', () => {
  // 清理所有缓存的模型
  for (const gltf of modelCache.values()) {
    disposeModel(gltf);
  }
  modelCache.clear();
  
  // 清理加载器
  dracoLoader.dispose();
});

export { dracoLoaderModel };
