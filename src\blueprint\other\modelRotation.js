/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/15 15时19分59秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 模型旋转
 */

import {useMainStore, useSettingStore} from "@/store/entra/index.js";
import {registerRenderFunc} from "@/utils/3d/animate.js";

/**
 * 场景旋转
 */
function modelRotation() {
    const mainStore = useMainStore();
    const mapSetting = useSettingStore();
    let scene = mainStore.scene;
    if (scene) {
        // 保存初始旋转角度
        mapSetting.initialRotationY = scene.rotation.y;
        registerRenderFunc("initModelRotation", () => {
            scene.rotation.y += 0.01; // 控制模型旋转速度
        });
    }
    //地库旋转
}

export {modelRotation};
