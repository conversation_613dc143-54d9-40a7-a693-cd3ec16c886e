/**
 * 防抖
 * func: 点击按钮要执行的函数
 * delay: 防抖时间
 */
//mousemove change 等
export function debounce(func, delay) {
    // 设置定时器标识
    let timer;
    delay = delay || 1000;
    // 难点返回事件绑定函数
    return function () {
        // func指定this
        let context = this;
        // func参数
        let args = arguments;
        // 先清除定时器
        clearTimeout(timer);
        //设置定时器
        timer = setTimeout(() => {
            // 调用函数
            func.apply(context, args);
        }, delay);
    };
}

// 节流
//click scroll 等
export function throttle(fn, time) {
    let timer = null;
    time = time || 1000;
    return function (...args) {
        if (timer) {
            return;
        }
        const _this = this;
        timer = setTimeout(() => {
            timer = null;
        }, time);
        fn.apply(_this, args);
    };
}
