<template>
  <use-charts :options="option" class="h-full w-full"></use-charts>
</template>
<script setup name="ParkingRight1">
  import UseCharts from '@/common/useCharts.vue'
  import * as echarts from 'echarts'
  import { onMounted } from 'vue'
  import request from '@/utils/request.js'
  const salvProName = ['A', 'B', 'C', 'D', 'E']
  const salvProValue = ref([])
  const salvProMax = [] //背景按最大值
  const option = ref({})

  onMounted(() => {
    request({
      url: '/hardware/parking/getParkSpaceInfo',
      method: 'get',
      data: {
        parkId: '',
      },
    }).then((request) => {
      let parkingData = request.data.area_info
      parkingData.sort((a, b) => {
        if (a.area_name < b.area_name) {
          return -1
        }
        if (a.area_name > b.area_name) {
          return 1
        }
        return 0
      })
      console.log(parkingData)
      salvProValue.value.push(...parkingData.map((a) => a.empty_parking_space))
      for (let i = 0; i < salvProValue.value.length; i++) {
        salvProMax.push(salvProValue.value[0])
      }

      option.value = {
        backgroundColor: '',
        grid: {
          left: '2%',
          right: '2%',
          bottom: '12%',
          top: '2%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
          },
          formatter: function (params) {
            return params[0].name + ' : ' + params[0].value + '个'
          },
        },
        xAxis: {
          show: false,
          type: 'value',
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#fff',
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            data: salvProName,
          },
          {
            type: 'category',
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            show: true,
            axisLabel: {
              textStyle: {
                color: '#ffffff',
                fontSize: '12',
              },
            },
            data: salvProValue,
          },
        ],
        series: [
          {
            name: '值',
            type: 'bar',
            zlevel: 1,
            itemStyle: {
              normal: {
                barBorderRadius: 30,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: 'rgb(57,89,255,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgb(46,200,207,1)',
                  },
                ]),
              },
            },
            barWidth: 20,
            data: salvProValue,
          },
          {
            name: '背景',
            type: 'bar',
            barWidth: 20,
            barGap: '-100%',
            data: salvProMax,
            itemStyle: {
              normal: {
                color: 'rgba(24,31,68,0)',
                barBorderRadius: 30,
              },
            },
          },
        ],
      }
    })
  })
</script>

<style scoped></style>
