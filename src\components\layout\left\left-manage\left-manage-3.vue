<template>
  <div class="container">
    <div class="item" v-for="(item, index) in items" :key="index">
      <span class="mr-[5px]">{{ item.title }}</span>
      <span>{{ item.value }}</span>
    </div>
  </div>
  <div class="mt-[5px] mb-[5px] text-[#54D8EA]">区域床位统计</div>
  <div class="grid-container">
    <div class="grid-row" v-for="(floor, index) in floors" :key="index">
      <div class="grid-item">
        <div class="floor">{{ floor.label }}</div>
        <div class="bed">床位数 {{ floor.beds }}</div>
      </div>
    </div>
  </div>
</template>

<script setup name="ManageLeft3">
const items = [
  {title: "床位数", value: 841},
  {title: "已用", value: 810},
  {title: "未用", value: 31},
];
const floors = Array.from({length: 8}, (_, i) => ({
  label: `${i + 1}F`,
  beds: Math.floor(Math.random() * 31) + 20,
}));
</script>
<style scoped>
.container {
  display: flex;
  justify-content: space-around;
  color: #fff;
}

.item {
  flex: 1;
  text-align: center;
}

.item > div {
  display: flex;
  justify-content: center;
  align-items: center;
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.grid-row {
  width: 48%;
  margin-bottom: 0px;
}

.grid-item {
  display: flex;
  justify-content: space-between;
  padding: 3px;
  line-height: 12px;
}

.floor {
  background-color: #0e1e33;
  color: white;
  padding: 6px;
}

.bed {
  background-color: #021429;
  color: white;
  padding: 6px;
}
</style>
