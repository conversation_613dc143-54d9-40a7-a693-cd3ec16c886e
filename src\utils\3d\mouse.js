/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/18 10时40分02秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 鼠标交互
 *
 */
import { useMainStore } from '@/store/entra/index.js'
import * as THREE from 'three'

/**
 * @description 鼠标左键双击
 * @param event 点击event事件
 */
function dbClick(event) {
  if (event.detail === 2 && event.button === 0) {
    const mainStore = useMainStore()
    let { intersects, firstName } = genericEvents(event)
    //双击左键
    if (intersects.length > 0) {
      if (event.detail === 2) {
        //执行事件
      }
    }
  }
}

/**
 * @description 鼠标右键双击
 * @type {number}
 */
let t = 0,
  total = 0

function dbRClick(element = document.getElementById('scene')) {
  const mainStore = useMainStore()
  let { roomCollection, gltfAnimate, labelCollection } = mainStore
  element.addEventListener('mousedown', (event) => {
    if (event.button !== 2) return
    total += 1
    if (total === 1) {
      t = new Date().valueOf()
    }
    if (total === 2) {
      let now = new Date().valueOf()
      if (now - t < 300) {
        //执行鼠标右键双击的事件

        total = 0
        t = 0
      } else {
        total = 1
        t = now
      }
    }
  })
}

/**
 * @description 点击事件检测方法
 * @param event 点击event事件
 */
function genericEvents(event) {
  const mainStore = useMainStore()
  let { scene, camera } = mainStore

  // 创建一个归一化设备坐标 (NDC) 的向量
  let vector = getDivVector(event)
  /*  let vector = new THREE.Vector3(
    (event.clientX / window.innerWidth) * 2 - 1,
    -(event.clientY / window.innerHeight) * 2 + 1,
    0.5
  )*/

  let mouse = JSON.parse(JSON.stringify(vector))
  vector = vector.unproject(camera) //用摄像机的投影矩阵解压矢量
  let vectorCopy = JSON.parse(JSON.stringify(vector))
  let raycaster = new THREE.Raycaster(camera.position, vector.sub(camera.position).normalize())
  let intersects, firstName
  if (mainStore.model.visible) {
    try {
      intersects = raycaster.intersectObject(mainStore.model, true)
      if (intersects[0]) {
        firstName = intersects[0].object.name
      }
    } catch (e) {
      console.log(e)
    }
  }

  return {
    intersects: intersects,
    firstName: firstName,
    vectorCopy: vectorCopy,
    mouse: mouse,
  }
}
//在窗口中移动鼠标获取的坐标
function getDivVector(event) {
  // 获取 div 元素
  let sceneDiv = document.getElementById('scene')

  // 获取 div 的边界尺寸
  let rect = sceneDiv.getBoundingClientRect()

  // 计算鼠标在 div 内的相对位置
  let mouseX = ((event.clientX - rect.left) / rect.width) * 2 - 1
  let mouseY = (-(event.clientY - rect.top) / rect.height) * 2 + 1

  // 创建一个归一化设备坐标 (NDC) 的向量
  return new THREE.Vector3(mouseX, mouseY, 0.5)
}
function loadSceneWidthANDHeight() {
  let sceneDiv = document.getElementById('scene')

  // 获取 div 的边界尺寸
  let rect = sceneDiv.getBoundingClientRect()

  return {
    width: rect.width,
    height: rect.height,
  }
}

export { genericEvents, getDivVector, loadSceneWidthANDHeight }
