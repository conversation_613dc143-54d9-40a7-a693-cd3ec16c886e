/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/6 10时09分15秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 测试绘制路径
 */

import { useMainStore } from '@/store/entra/index.js'
import { genericEvents } from '@/utils/3d/mouse.js'
import { drawLine } from '@/utils/3d/drawLine.js'

/**
 * 左键开始画路径
 * @param event 鼠标event事件
 */
function startDrawPath(event) {
  const mainStore = useMainStore()
  if (event.button === 0) {
    let { firstName, intersects } = genericEvents(event)
    //开启绘制路径后 判断是否点击上房间上 进行绘制
    if (
      firstName &&
      mainStore.guiTest.drawing
      //白膜地库
      // firstName.includes("楼板_100_944339")
    ) {
      console.log(firstName)
      const drawLine = mainStore.startDrawLine().drawLine
      drawLine.add(intersects)
    }
  }

  if (event.button === 1 && mainStore.draw.openRight) {
    //开启绘制路径后 判断是否点击上房间上 进行绘制
    if (
      mainStore.guiTest.drawing
      //白膜地库
    ) {
      const drawLine = mainStore.startDrawLine().drawLine
      drawLine.savePath()
      console.log(mainStore.draw.pathArray)
      mainStore.eventDom.removeEvent('mousedown', startDrawPath)
    }
  }
}

function isDrawing(boolean) {
  const mainStore = useMainStore()
  if (boolean) {
    mainStore.eventDom.addEvent('mousedown', startDrawPath)
  } else {
    mainStore.eventDom.removeEvent('mousedown', startDrawPath)
  }
}

export { isDrawing }
