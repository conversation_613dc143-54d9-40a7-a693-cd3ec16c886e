<template>
  <div class="absolute bottom-[8vh] left-[50%] translate-x-[-50%] w-[449px]">
    <div class="w-[449px] h-[34px]">
      <div class="flex" id="roamId">
        <div
          v-for="(item, index) in divs"
          :key="index"
          :class="index === selectedIndex ? 'selected' : 'box'"
          class="h-[32px] bg-no-repeat bg-contain bg-center flex-1 grid place-items-center cursor-pointer"
          @click="selectDiv(index)"
        >
          <span class="box-text">{{ item.text }}</span>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-center" v-show="selectedIndex !== -1">
      <div class="flex items-center justify-between w-[200px] mt-[10px] box p-[5px]">
        <n-icon :component="IosPause" class="cursor-pointer" size="20" @click="pause" />
        <n-icon :component="IosPlay" class="cursor-pointer" size="20" @click="play" />
        <n-icon :component="MdLogOut" class="cursor-pointer" size="20" @click="logout" />
      </div>
    </div>
  </div>
  <div
    class="absolute items-center justify-center top-[8vh] right-[20vw] translate-x-[-50%] w-[300px] cursor-pointer"
  >
    <p class="float-left text-[#fff] text-[14px] mt-[5px]">查询车牌:</p>
    <n-input
      class="items-center justify-center !w-[100px] !p-[0px] ml-[10px]"
      v-model:value="carNumber"
      type="text"
      placeholder="请输入"
    />
    <n-button @click="searchForCarNumber" type="info">查询</n-button>
  </div>
</template>

<script setup>
  import { leaveCar, revertCar } from '@/blueprint/undergroundParking/car.js'
  import { logoutRenderFunc } from '@/utils/3d/animate.js'
  import { useMainStore, useSettingStore } from '@/store/entra/index.js'
  import { dynamicArrows, revertArrow } from '@/blueprint/undergroundParking/arrow.js'
  import { NIcon, useNotification } from 'naive-ui'
  import { IosPause, IosPlay, MdLogOut } from '@vicons/ionicons4'
  import * as THREE from 'three'
  import { flyTo } from '@/utils/3d/camera.js'
  import { ref } from 'vue'
  import request from '@/utils/request.js'
  import { ElMessage } from 'element-plus'

  let selectedIndex = ref(-1)
  const mapSetting = useSettingStore()
  const mainStore = useMainStore()

  const divs = [
    { text: 'A区漫游' },
    { text: 'B区漫游' },
    { text: 'C区漫游' },
    { text: 'D区漫游' },
    { text: 'E区漫游' },
  ]

  function selectDiv(index) {
    selectedIndex.value = index
    startMoving(index)
  }

  function startMoving(index) {
    //停止旋转
    logoutRenderFunc('initModelRotation')
    let params = mapSetting.testCarLeaveLineArray[index]
    if (params.roadLine.length > 0) {
      //开始播放
      mapSetting.underGroundCarPause = false
      dynamicArrows(params.roadLine)
      leaveCar(params)
    }
  }

  function pause() {
    mapSetting.underGroundCarPause = true
  }

  function play() {
    mapSetting.underGroundCarPause = false
  }

  function logout() {
    //汽车 还有动态线
    //停止动画
    mapSetting.underGroundCarPause = false
    let name = divs[selectedIndex.value].text
    revertCar(name)
    revertArrow()
  }

  const carNumber = ref('')
  const notification = useNotification()

  function validateCarNumber(value, callback) {
    if (!value) {
      ElMessage({ message: '请输入车牌号', type: 'error' })
    } else {
      // 校验车牌号的正则表达式（排除字母 "O" 和 "I"）
      const regularCarNumber = /^[\u4e00-\u9fa5]{1}[A-NP-Za-np-z]{1}[A-NP-Za-np-z_0-9]{5}$/ // 普通车牌号正则（排除 "O" 和 "I"）
      const newEnergyCarNumber = /^[\u4e00-\u9fa5]{1}D[A-NP-Za-np-z_0-9]{6}$/ // 新能源车牌号正则（排除 "O" 和 "I"）

      if (regularCarNumber.test(value) || newEnergyCarNumber.test(value)) {
        callback() // 校验通过
      } else {
        ElMessage({ message: '请输入合法的车牌号', type: 'error' })
      }
    }
  }

  function searchForCarNumber() {
    const mainStore = useMainStore()
    let { scene, camera, controls, renderer } = mainStore
    let name = 'DK-A1-003'
    validateCarNumber(carNumber.value, function () {
      let targetModel = mainStore.model.getObjectByName(name)
      return request({
        url: '/hardware/parking/getPlateNoInfo',
        method: 'get',
        params: {
          parkingLotNo: carNumber.value,
        },
      }).then((response) => {
        const data = response.data
        if (targetModel) {
          notification.success({
            content: '所在车位:' + data.parking_lot_no,
            meta: '',
            duration: 2500,
            keepAliveOnHover: true,
          })
          const cubeCenter = new THREE.Vector3()
          targetModel.getWorldPosition(cubeCenter)
          // 设置摄像机的位置为正方体的正面视图位置
          let cameraPosition = cubeCenter.clone().add(new THREE.Vector3(0, 5, 0))
          flyTo(camera, controls, cameraPosition, cubeCenter, 2000, function () {
            useMainStore().queryCarByName = name + '-CAR'
            const car = scene.getObjectByName(name + '-CAR')
            car.material.opacity = 1
            controls.update()
            // 渲染场景
            renderer.render(scene, camera)
          })
        } else {
          notification.warning({
            content: '没找到',
            meta: '',
            duration: 2500,
            keepAliveOnHover: true,
          })
        }
      })
    })
  }
</script>

<style scoped>
  .selected {
    background-image: url('@/assets/images/bottom-dk-select.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: #fff;
  }

  .box {
    background-image: url('@/assets/images/bottom-dk-noSelect.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: #fff;
  }
</style>
