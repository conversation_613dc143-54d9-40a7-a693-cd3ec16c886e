/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/6 09时51分08秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 绘制路线
 */
import * as THREE from 'three'
import { Line2 } from 'three/examples/jsm/lines/Line2.js'
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js'
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js'
import { useMainStore } from '@/store/entra/index.js'
import { drawPoint } from '@/utils/3d/drawPoint.js'
import { markRaw } from 'vue'
import { loadSceneWidthANDHeight } from '@/utils/3d/mouse.js'

/**
 * @constructor  通过线段渲染  绘制更新
 */
export class drawLine {
  constructor(
    config = {
      color: 0x00ff00,
      linewidth: 6,
    }
  ) {
    const mainStore = useMainStore()
    let { scene, model, setSavePath } = mainStore
    let { cruisingCollection, pointArrays, pathArray } = mainStore.draw
    this.scene = scene
    this.model = model
    this.config = config
    // this.setSavePath = setSavePath;
    this.cruisingCollection = cruisingCollection
    //初始化
    this.mousePosition = []
    this.pointArrays = pointArrays
    this.pathArray = pathArray
    this.setSavePath = setSavePath
    //这里初始化一下 点的函数
    this.Point = new drawPoint('巡检点', cruisingCollection)
  }

  add(object) {
    this.mousePosition = []
    object[0].point.y = -5.6
    //点击的点存一下
    this.mousePosition.push(object[0].point)
    this.pointArrays.push(this.Point.add([object[0].point]))
    if (this.cruisingCollection.children.length > 1) {
      //更新逻辑
      if (this.pointArrays.length >= 2) {
        this.update()
      }
    }
  }

  update() {
    let no1 = new THREE.Vector3(
      this.pointArrays[0].geometry.attributes.position.array[0],
      -5.6,
      this.pointArrays[0].geometry.attributes.position.array[2]
    )
    let no2 = new THREE.Vector3(
      this.pointArrays[1].geometry.attributes.position.array[0],
      -5.6,
      this.pointArrays[1].geometry.attributes.position.array[2]
    )
    this.mousePosition.push(no1, no2)

    const positions = this.mousePosition.reduce((acc, item) => {
      acc.push(item.x, item.y, item.z)
      return acc
    }, [])

    let lineGeometry = new LineGeometry()
    lineGeometry.setPositions(positions)
    let lineMaterial = new LineMaterial(this.config)
    let { width, height } = loadSceneWidthANDHeight()
    lineMaterial.resolution.set(width, height)
    this.line = new Line2(lineGeometry, lineMaterial)
    this.line.name = '巡检路线'
    this.pointArrays.shift()
    this.cruisingCollection.add(this.line)
    if (this.mousePosition.length > 2) {
      this.setSavePath(true)
    }
  }

  savePath() {
    const mainStore = useMainStore()
    this.pointArrays = markRaw([])
    this.destroy()
    this.temp.reverse()
    // this.temp.push(this.temp[0]);
    this.pathArray.push(this.temp)
  }

  // 将给定坐标数组转换为 THREE.Vector3 数组的函数

  destroy() {
    this.temp = []
    for (let i = this.cruisingCollection.children.length - 1; i >= 0; i--) {
      let obj = this.cruisingCollection.children[i]
      if (obj.name === '巡检点') {
        this.temp.push(obj.geometry.attributes.position.array)
        this.cruisingCollection.remove(obj)
      } else if (obj.name === '巡检路线') {
        this.cruisingCollection.remove(obj)
      }
    }
    this.cruisingCollection.clear()
  }
}
