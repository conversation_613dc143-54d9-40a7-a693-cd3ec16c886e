{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 65532, "max": [3.9268460273742676, 8.342679023742676, 3.9585349559783936], "min": [-3.9268341064453125, -8.348748207092285, 0.4463370144367218], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 786384, "componentType": 5126, "count": 65532, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 65532, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "componentType": 5125, "count": 363438, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1572768, "componentType": 5126, "count": 5550, "max": [3.925647020339966, 2.2280449867248535, 3.9575259685516357], "min": [-3.9268341064453125, -8.0897798538208, 2.889349937438965], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1639368, "componentType": 5126, "count": 5550, "max": [1.0, 0.999971330165863, 1.0], "min": [-0.999998152256012, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 524256, "componentType": 5126, "count": 5550, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1453752, "componentType": 5125, "count": 23625, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1705968, "componentType": 5126, "count": 1066, "max": [2.8619120121002197, 8.342679023742676, 3.9074039459228516], "min": [-2.861907958984375, -8.085693359375, 1.4513089656829834], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1718760, "componentType": 5126, "count": 1066, "max": [0.9965418577194214, 0.9957039952278137, 0.999896764755249], "min": [-0.9965406656265259, -0.8731051087379456, -0.9949744343757629], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 568656, "componentType": 5126, "count": 1066, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1548252, "componentType": 5125, "count": 4176, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1731552, "componentType": 5126, "count": 3093, "max": [2.7719650268554688, 8.325161933898926, 4.010725975036621], "min": [-2.7719600200653076, -3.6643519401550293, 1.4783849716186523], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1768668, "componentType": 5126, "count": 3093, "max": [0.999572217464447, 0.9977790713310242, 0.9998037219047546], "min": [-0.9995710849761963, -0.9997599720954895, -0.9853767156600952], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 577184, "componentType": 5126, "count": 3093, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1564956, "componentType": 5125, "count": 16800, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1805784, "componentType": 5126, "count": 65532, "max": [3.9311490058898926, 8.180841445922852, 4.333633899688721], "min": [-3.9311299324035645, -8.071147918701172, 0.4878300130367279], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 2592168, "componentType": 5126, "count": 65532, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 601928, "componentType": 5126, "count": 65532, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 1632156, "componentType": 5125, "count": 322785, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 3378552, "componentType": 5126, "count": 44511, "max": [3.2922511100769043, 6.50098180770874, 4.340915203094482], "min": [-3.292185068130493, -8.096677780151367, 0.6558510065078735], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 3912684, "componentType": 5126, "count": 44511, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1126184, "componentType": 5126, "count": 44511, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 2923296, "componentType": 5125, "count": 209766, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4446816, "componentType": 5126, "count": 12329, "max": [2.2149980068206787, -4.772846221923828, 3.5222740173339844], "min": [-2.2149999141693115, -8.037698745727539, 2.7843759059906006], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4594764, "componentType": 5126, "count": 12329, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1482272, "componentType": 5126, "count": 12329, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 3762360, "componentType": 5125, "count": 63762, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4742712, "componentType": 5126, "count": 2813, "max": [1.181210994720459, -7.446416854858398, 2.4402430057525635], "min": [-1.1812130212783813, -8.345203399658203, 1.8347439765930176], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4776468, "componentType": 5126, "count": 2813, "max": [0.9999995827674866, 0.7423185706138611, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1580904, "componentType": 5126, "count": 2813, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4017408, "componentType": 5125, "count": 13410, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4810224, "componentType": 5126, "count": 817, "max": [3.3981220722198486, 8.178241729736328, 2.64776611328125], "min": [-3.3951220512390137, 1.0999139547348022, 1.6105600595474243], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 4820028, "componentType": 5126, "count": 817, "max": [0.991676390171051, 0.9969263672828674, 0.99579256772995], "min": [-0.9916762113571167, -0.9969254732131958, -0.9026402235031128], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1603408, "componentType": 5126, "count": 817, "max": [0.9748268127441406, 0.9526708126068115], "min": [0.07162422686815262, 0.020473415032029152], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4071048, "componentType": 5125, "count": 3918, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 4829832, "componentType": 5126, "count": 39131, "max": [3.4837419986724854, 8.277325630187988, 4.192625045776367], "min": [-3.483738899230957, -8.285747528076172, 0.4463390111923218], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 5299404, "componentType": 5126, "count": 39131, "max": [1.0, 1.0, 1.0], "min": [-1.0, -0.9999819993972778, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1609944, "componentType": 5126, "count": 39131, "max": [28.47351837158203, 28.55649185180664], "min": [-27.341182708740234, -27.421245574951172], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4086720, "componentType": 5125, "count": 194682, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 5768976, "componentType": 5126, "count": 40597, "max": [2.826972007751465, 3.523252010345459, 3.848304033279419], "min": [-2.7999820709228516, -1.835561990737915, 0.6743040084838867], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 6256140, "componentType": 5126, "count": 40597, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -0.9999998211860657], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1922992, "componentType": 5126, "count": 40597, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 4865448, "componentType": 5125, "count": 172587, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 6743304, "componentType": 5126, "count": 33532, "max": [3.069382905960083, 6.517622947692871, 2.9854140281677246], "min": [-3.06938099861145, -7.812230110168457, 1.93116295337677], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7145688, "componentType": 5126, "count": 33532, "max": [1.0, 1.0, 1.0], "min": [-1.0, -0.9995213150978088, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2247768, "componentType": 5126, "count": 33532, "max": [20.958723068237305, 6.473879337310791], "min": [-20.40619659423828, -5.5003581047058105], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5555796, "componentType": 5125, "count": 43761, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7548072, "componentType": 5126, "count": 4727, "max": [3.17740797996521, 7.547882080078125, 2.1467320919036865], "min": [-3.1774001121520996, 5.686476230621338, 1.4362390041351318], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7604796, "componentType": 5126, "count": 4727, "max": [1.0, 0.9962475299835205, 0.9997815489768982], "min": [-1.0, -0.00036614862619899213, -0.9999945759773254], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2516024, "componentType": 5126, "count": 4727, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5730840, "componentType": 5125, "count": 24036, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7661520, "componentType": 5126, "count": 979, "max": [3.1404850482940674, 7.674332141876221, 2.0093300342559814], "min": [-3.1404850482940674, -8.046332359313965, 0.6935380101203918], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7673268, "componentType": 5126, "count": 979, "max": [0.9897066950798035, 0.8586421608924866, 0.6328051686286926], "min": [-0.9936841130256653, -0.9999276995658875, -0.5019388198852539], "type": "VEC3"}, {"bufferView": 3, "componentType": 5126, "count": 979, "max": [0.9999911189079285, 0.9996452927589417, 0.9848799109458923, 1.0], "min": [-0.9979540109634399, -0.9996454119682312, -0.9848795533180237, -1.0], "type": "VEC4"}, {"bufferView": 1, "byteOffset": 2553840, "componentType": 5126, "count": 979, "max": [73.92062377929688, 16.809999465942383], "min": [-4.400707721710205, -2.551671266555786], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5826984, "componentType": 5125, "count": 4224, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7685016, "componentType": 5126, "count": 1170, "max": [3.1678619384765625, 6.782357215881348, 1.930426001548767], "min": [-3.167862892150879, 6.066638946533203, 1.7093110084533691], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7699056, "componentType": 5126, "count": 1170, "max": [0.827585756778717, 0.6095881462097168, 0.9987452030181885], "min": [-0.8276098966598511, 0.0, -0.9960896968841553], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2561672, "componentType": 5126, "count": 1170, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5843880, "componentType": 5125, "count": 5184, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7713096, "componentType": 5126, "count": 442, "max": [3.5184168815612793, 7.606194972991943, 2.6843559741973877], "min": [-3.5184168815612793, -7.308537006378174, 0.4668479859828949], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7718400, "componentType": 5126, "count": 442, "max": [1.0, 0.999173104763031, 0.34402021765708923], "min": [-1.0, -0.9997546076774597, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2571032, "componentType": 5126, "count": 442, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5864616, "componentType": 5125, "count": 1488, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7723704, "componentType": 5126, "count": 9559, "max": [3.9021880626678467, 1.6917049884796143, 3.6712729930877686], "min": [-3.9021589756011963, -8.197624206542969, 2.6259889602661133], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 7838412, "componentType": 5126, "count": 9559, "max": [0.9999924302101135, 0.9999994039535522, 1.0], "min": [-0.9999924302101135, -0.999999463558197, -0.9997988343238831], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2574568, "componentType": 5126, "count": 9559, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 5870568, "componentType": 5125, "count": 32706, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 7953120, "componentType": 5126, "count": 4683, "max": [2.84843111038208, 4.405314922332764, 3.960732936859131], "min": [-2.8484299182891846, -7.823122024536133, 2.499994993209839], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8009316, "componentType": 5126, "count": 4683, "max": [1.0, 1.0, 1.0], "min": [-1.0, -0.9999161958694458, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2651040, "componentType": 5126, "count": 4683, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6001392, "componentType": 5125, "count": 25851, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8065512, "componentType": 5126, "count": 51, "max": [-0.5240539908409119, 2.1749160289764404, 3.056641101837158], "min": [-1.85788094997406, 2.1238179206848145, 2.48659610748291], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8066124, "componentType": 5126, "count": 51, "max": [0.0001492706942372024, -0.9974527955055237, 0.07125875353813171], "min": [-0.0032265607733279467, -0.9997835755348206, 0.020805973559617996], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2688504, "componentType": 5126, "count": 51, "max": [0.9830350279808044, 0.9445765018463135], "min": [0.06038033217191696, 0.5893329977989197], "type": "VEC2"}, {"bufferView": 1, "byteOffset": 2688912, "componentType": 5126, "count": 51, "max": [0.9830350279808044, 0.9445765018463135], "min": [0.06038033217191696, 0.5893329977989197], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6104796, "componentType": 5125, "count": 156, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8066736, "componentType": 5126, "count": 8871, "max": [2.94909405708313, -7.274336814880371, 3.0465850830078125], "min": [-2.9490981101989746, -8.047072410583496, 1.2337640523910522], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8173188, "componentType": 5126, "count": 8871, "max": [1.0, 0.007148304954171181, 1.0], "min": [-1.0, -0.9999586343765259, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2689320, "componentType": 5126, "count": 8871, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6105420, "componentType": 5125, "count": 46374, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8279640, "componentType": 5126, "count": 1176, "max": [2.9300549030303955, -7.367492198944092, 3.031079053878784], "min": [-2.930061101913452, -7.907482147216797, 2.7147860527038574], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8293752, "componentType": 5126, "count": 1176, "max": [1.0, 0.0, 1.0], "min": [-1.0, -0.9921441078186035, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2760288, "componentType": 5126, "count": 1176, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6290916, "componentType": 5125, "count": 4032, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8307864, "componentType": 5126, "count": 37, "max": [1.4600499868392944, -4.933797836303711, 3.2376930713653564], "min": [-1.4600520133972168, -7.086440086364746, 2.908643960952759], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8308308, "componentType": 5126, "count": 37, "max": [0.9136750102043152, 1.0, 0.9921719431877136], "min": [-0.9136750102043152, -1.0, 0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2769696, "componentType": 5126, "count": 37, "max": [0.8413658738136292, 0.7505776286125183], "min": [0.1586342453956604, 0.22626663744449615], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6307044, "componentType": 5125, "count": 72, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8308752, "componentType": 5126, "count": 7654, "max": [2.793142080307007, 2.798429012298584, 3.472562074661255], "min": [-2.7999820709228516, -1.7189559936523438, 0.6797279715538025], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8400600, "componentType": 5126, "count": 7654, "max": [0.999987006187439, 0.9995958805084229, 0.9999998807907104], "min": [-0.999983549118042, -0.9998981356620789, -0.9999112486839294], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2769992, "componentType": 5126, "count": 7654, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6307332, "componentType": 5125, "count": 37620, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8492448, "componentType": 5126, "count": 1621, "max": [1.2749099731445312, 1.3881980180740356, 3.214026927947998], "min": [-1.5666439533233643, -1.7395620346069336, 0.6836029887199402], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8511900, "componentType": 5126, "count": 1621, "max": [0.8321101069450378, 0.9988142251968384, 0.9999985098838806], "min": [-0.8120150566101074, -1.0, -0.9984917640686035], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2831224, "componentType": 5126, "count": 1621, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6457812, "componentType": 5125, "count": 8406, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8531352, "componentType": 5126, "count": 2406, "max": [1.878872036933899, 2.85158109664917, 2.994626045227051], "min": [-2.563170909881592, -0.7581409811973572, 0.8159220218658447], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8560224, "componentType": 5126, "count": 2406, "max": [0.9868799448013306, 0.9999993443489075, 0.9999980926513672], "min": [-0.999595582485199, -0.9994357228279114, -0.9999999403953552], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2844192, "componentType": 5126, "count": 2406, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6491436, "componentType": 5125, "count": 11472, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8589096, "componentType": 5126, "count": 41, "max": [1.301145315170288, -0.40268540382385254, 2.228468894958496], "min": [1.0139912366867065, -1.897573471069336, 1.62621009349823], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8589588, "componentType": 5126, "count": 41, "max": [0.9529802203178406, -0.007957245223224163, 0.5208083391189575], "min": [0.8533671498298645, -0.06651262938976288, 0.295643150806427], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2863440, "componentType": 5126, "count": 41, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6537324, "componentType": 5125, "count": 171, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8590080, "componentType": 5126, "count": 4226, "max": [3.1969399452209473, 8.348769187927246, 0.8344799876213074], "min": [-3.196937084197998, 6.222880840301514, 0.45389100909233093], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8640792, "componentType": 5126, "count": 4226, "max": [0.9823277592658997, 0.9895681738853455, 0.9999999403953552], "min": [-0.9823417067527771, -1.0, -0.99925297498703], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2863768, "componentType": 5126, "count": 4226, "max": [11.499999046325684, 4.146124839782715], "min": [-10.499999046325684, -3.1558871269226074], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6538008, "componentType": 5125, "count": 21264, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8691504, "componentType": 5126, "count": 4800, "max": [0.6377911567687988, 1.2061861753463745, 1.206190824508667], "min": [-0.23576688766479492, -1.2060917615890503, -1.2060871124267578], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 8749104, "componentType": 5126, "count": 4800, "max": [0.9999841451644897, 0.9998215436935425, 0.9998213648796082], "min": [-0.9992058873176575, -0.9998214244842529, -0.9998215436935425], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2897576, "componentType": 5126, "count": 4800, "max": [0.9850136041641235, 0.9850155115127563], "min": [0.014982819557189941, 0.01498463749885559], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6623064, "componentType": 5125, "count": 24480, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 8806704, "componentType": 5126, "count": 22121, "max": [0.6630923748016357, 0.9758556485176086, 0.9772238731384277], "min": [-0.22794556617736816, -0.9757487177848816, -0.9757551550865173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9072156, "componentType": 5126, "count": 22121, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2935976, "componentType": 5126, "count": 22121, "max": [0.89955735206604, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 6720984, "componentType": 5125, "count": 107313, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 9337608, "componentType": 5126, "count": 6288, "max": [-0.004322528373450041, 0.9758556485176086, 0.9772238731384277], "min": [-0.23147249221801758, -0.9757487177848816, -0.9757551550865173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9413064, "componentType": 5126, "count": 6288, "max": [0.6943983435630798, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3112944, "componentType": 5126, "count": 6288, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7150236, "componentType": 5125, "count": 33174, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 9488520, "componentType": 5126, "count": 145, "max": [-0.03433346748352051, 0.10312964767217636, 0.10312782227993011], "min": [-0.04704761505126953, -0.10302970558404922, -0.10303021967411041], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9490260, "componentType": 5126, "count": 145, "max": [-0.5729324221611023, 0.8085758686065674, 0.8086839318275452], "min": [-1.0, -0.8086596131324768, -0.8085504174232483], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3163248, "componentType": 5126, "count": 145, "max": [0.5760127305984497, 0.30331656336784363], "min": [0.3255411684513092, 0.05462459474802017], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7282932, "componentType": 5125, "count": 648, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 9492000, "componentType": 5126, "count": 2788, "max": [0.3368043899536133, 0.6861894726753235, 0.6861878633499146], "min": [0.20059943199157715, -0.6860887408256531, -0.6860901713371277], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9525456, "componentType": 5126, "count": 2788, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3164408, "componentType": 5126, "count": 2788, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7285524, "componentType": 5125, "count": 12417, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 9558912, "componentType": 5126, "count": 4800, "max": [0.6377911567687988, 1.2061861753463745, 1.206190824508667], "min": [-0.23576688766479492, -1.2060917615890503, -1.2060871124267578], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9616512, "componentType": 5126, "count": 4800, "max": [0.9999841451644897, 0.9998215436935425, 0.9998213648796082], "min": [-0.9992058873176575, -0.9998214244842529, -0.9998215436935425], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3186712, "componentType": 5126, "count": 4800, "max": [0.9850136041641235, 0.9850155115127563], "min": [0.014982819557189941, 0.01498463749885559], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7335192, "componentType": 5125, "count": 24480, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 9674112, "componentType": 5126, "count": 22121, "max": [0.6630923748016357, 0.9758556485176086, 0.9772238731384277], "min": [-0.22794556617736816, -0.9757487177848816, -0.9757551550865173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9939564, "componentType": 5126, "count": 22121, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3225112, "componentType": 5126, "count": 22121, "max": [0.89955735206604, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7433112, "componentType": 5125, "count": 107313, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 10205016, "componentType": 5126, "count": 6288, "max": [-0.004322528373450041, 0.9758556485176086, 0.9772238731384277], "min": [-0.23147249221801758, -0.9757487177848816, -0.9757551550865173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 10280472, "componentType": 5126, "count": 6288, "max": [0.6943983435630798, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3402080, "componentType": 5126, "count": 6288, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7862364, "componentType": 5125, "count": 33174, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 10355928, "componentType": 5126, "count": 145, "max": [-0.03433346748352051, 0.10312964767217636, 0.10312782227993011], "min": [-0.04704761505126953, -0.10302970558404922, -0.10303021967411041], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 10357668, "componentType": 5126, "count": 145, "max": [-0.5729324221611023, 0.8085758686065674, 0.8086839318275452], "min": [-1.0, -0.8086596131324768, -0.8085504174232483], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3452384, "componentType": 5126, "count": 145, "max": [0.5760127305984497, 0.30331656336784363], "min": [0.3255411684513092, 0.05462459474802017], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7995060, "componentType": 5125, "count": 648, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 10359408, "componentType": 5126, "count": 2788, "max": [0.3368043899536133, 0.6861894726753235, 0.6861878633499146], "min": [0.20059943199157715, -0.6860887408256531, -0.6860901713371277], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 10392864, "componentType": 5126, "count": 2788, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3453544, "componentType": 5126, "count": 2788, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 7997652, "componentType": 5125, "count": 12417, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 10426320, "componentType": 5126, "count": 4800, "max": [0.6377911567687988, 1.2061861753463745, 1.206190824508667], "min": [-0.23576688766479492, -1.2060917615890503, -1.2060871124267578], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 10483920, "componentType": 5126, "count": 4800, "max": [0.9999841451644897, 0.9998215436935425, 0.9998213648796082], "min": [-0.9992058873176575, -0.9998214244842529, -0.9998215436935425], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3475848, "componentType": 5126, "count": 4800, "max": [0.9850136041641235, 0.9850155115127563], "min": [0.014982819557189941, 0.01498463749885559], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 8047320, "componentType": 5125, "count": 24480, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 10541520, "componentType": 5126, "count": 22121, "max": [0.6630923748016357, 0.9758556485176086, 0.9772238731384277], "min": [-0.22794556617736816, -0.9757487177848816, -0.9757551550865173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 10806972, "componentType": 5126, "count": 22121, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3514248, "componentType": 5126, "count": 22121, "max": [0.89955735206604, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 8145240, "componentType": 5125, "count": 107313, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 11072424, "componentType": 5126, "count": 6288, "max": [-0.004322528373450041, 0.9758556485176086, 0.9772238731384277], "min": [-0.23147249221801758, -0.9757487177848816, -0.9757551550865173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 11147880, "componentType": 5126, "count": 6288, "max": [0.6943983435630798, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3691216, "componentType": 5126, "count": 6288, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 8574492, "componentType": 5125, "count": 33174, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 11223336, "componentType": 5126, "count": 145, "max": [-0.03433346748352051, 0.10312964767217636, 0.10312782227993011], "min": [-0.04704761505126953, -0.10302970558404922, -0.10303021967411041], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 11225076, "componentType": 5126, "count": 145, "max": [-0.5729324221611023, 0.8085758686065674, 0.8086839318275452], "min": [-1.0, -0.8086596131324768, -0.8085504174232483], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3741520, "componentType": 5126, "count": 145, "max": [0.5760127305984497, 0.30331656336784363], "min": [0.3255411684513092, 0.05462459474802017], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 8707188, "componentType": 5125, "count": 648, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 11226816, "componentType": 5126, "count": 2788, "max": [0.3368043899536133, 0.6861894726753235, 0.6861878633499146], "min": [0.20059943199157715, -0.6860887408256531, -0.6860901713371277], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 11260272, "componentType": 5126, "count": 2788, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3742680, "componentType": 5126, "count": 2788, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 8709780, "componentType": 5125, "count": 12417, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 11293728, "componentType": 5126, "count": 4800, "max": [0.6377911567687988, 1.2061861753463745, 1.206190824508667], "min": [-0.23576688766479492, -1.2060917615890503, -1.2060871124267578], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 11351328, "componentType": 5126, "count": 4800, "max": [0.9999841451644897, 0.9998215436935425, 0.9998213648796082], "min": [-0.9992058873176575, -0.9998214244842529, -0.9998215436935425], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3764984, "componentType": 5126, "count": 4800, "max": [0.9850136041641235, 0.9850155115127563], "min": [0.014982819557189941, 0.01498463749885559], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 8759448, "componentType": 5125, "count": 24480, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 11408928, "componentType": 5126, "count": 22121, "max": [0.6630923748016357, 0.9758556485176086, 0.9772238731384277], "min": [-0.22794556617736816, -0.9757487177848816, -0.9757551550865173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 11674380, "componentType": 5126, "count": 22121, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3803384, "componentType": 5126, "count": 22121, "max": [0.89955735206604, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 8857368, "componentType": 5125, "count": 107313, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 11939832, "componentType": 5126, "count": 6288, "max": [-0.004322528373450041, 0.9758556485176086, 0.9772238731384277], "min": [-0.23147249221801758, -0.9757487177848816, -0.9757551550865173], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12015288, "componentType": 5126, "count": 6288, "max": [0.6943983435630798, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3980352, "componentType": 5126, "count": 6288, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9286620, "componentType": 5125, "count": 33174, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12090744, "componentType": 5126, "count": 145, "max": [-0.03433346748352051, 0.10312964767217636, 0.10312782227993011], "min": [-0.04704761505126953, -0.10302970558404922, -0.10303021967411041], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12092484, "componentType": 5126, "count": 145, "max": [-0.5729324221611023, 0.8085758686065674, 0.8086839318275452], "min": [-1.0, -0.8086596131324768, -0.8085504174232483], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4030656, "componentType": 5126, "count": 145, "max": [0.5760127305984497, 0.30331656336784363], "min": [0.3255411684513092, 0.05462459474802017], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9419316, "componentType": 5125, "count": 648, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12094224, "componentType": 5126, "count": 2788, "max": [0.3368043899536133, 0.6861894726753235, 0.6861878633499146], "min": [0.20059943199157715, -0.6860887408256531, -0.6860901713371277], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12127680, "componentType": 5126, "count": 2788, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4031816, "componentType": 5126, "count": 2788, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9421908, "componentType": 5125, "count": 12417, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12161136, "componentType": 5126, "count": 6183, "max": [0.4960692822933197, 0.7492610216140747, 0.6224331855773926], "min": [0.04436120763421059, 0.35267800092697144, -0.6118708252906799], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12235332, "componentType": 5126, "count": 6183, "max": [1.0, 1.0, 0.9999983310699463], "min": [-1.0, -1.0, -0.9999983906745911], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4054120, "componentType": 5126, "count": 6183, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9471576, "componentType": 5125, "count": 21024, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12309528, "componentType": 5126, "count": 2993, "max": [0.46534237265586853, 0.7582088708877563, 0.6582841873168945], "min": [0.06838425993919373, 0.3414708971977234, -0.6477288007736206], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12345444, "componentType": 5126, "count": 2993, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4103584, "componentType": 5126, "count": 2993, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9555672, "componentType": 5125, "count": 8688, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12381360, "componentType": 5126, "count": 6183, "max": [0.4960692822933197, 0.7492610216140747, 0.6224331855773926], "min": [0.04436120763421059, 0.35267800092697144, -0.6118708252906799], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12455556, "componentType": 5126, "count": 6183, "max": [1.0, 1.0, 0.9999983310699463], "min": [-1.0, -1.0, -0.9999983906745911], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4127528, "componentType": 5126, "count": 6183, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9590424, "componentType": 5125, "count": 21024, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12529752, "componentType": 5126, "count": 2993, "max": [0.46534237265586853, 0.7582088708877563, 0.6582841873168945], "min": [0.06838425993919373, 0.3414708971977234, -0.6477288007736206], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12565668, "componentType": 5126, "count": 2993, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4176992, "componentType": 5126, "count": 2993, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9674520, "componentType": 5125, "count": 8688, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12601584, "componentType": 5126, "count": 6173, "max": [0.5058043003082275, -0.28482139110565186, 0.6214408278465271], "min": [0.032657116651535034, -0.6866595149040222, -0.6292152404785156], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12675660, "componentType": 5126, "count": 6173, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -0.9999986290931702], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4200936, "componentType": 5126, "count": 6173, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9709272, "componentType": 5125, "count": 20994, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12749736, "componentType": 5126, "count": 2872, "max": [0.4736191928386688, -0.27348244190216064, 0.6577667593955994], "min": [0.05782315135002136, -0.6957404017448425, -0.6655492186546326], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12784200, "componentType": 5126, "count": 2872, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4250320, "componentType": 5126, "count": 2872, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9793248, "componentType": 5125, "count": 8685, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12818664, "componentType": 5126, "count": 6173, "max": [0.5058043003082275, -0.28482139110565186, 0.6214408278465271], "min": [0.032657116651535034, -0.6866595149040222, -0.6292152404785156], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 12892740, "componentType": 5126, "count": 6173, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -0.9999986290931702], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4273296, "componentType": 5126, "count": 6173, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9827988, "componentType": 5125, "count": 20994, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 12966816, "componentType": 5126, "count": 2872, "max": [0.4736191928386688, -0.27348244190216064, 0.6577667593955994], "min": [0.05782315135002136, -0.6957404017448425, -0.6655492186546326], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 13001280, "componentType": 5126, "count": 2872, "max": [1.0, 1.0, 1.0], "min": [-1.0, -1.0, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4322680, "componentType": 5126, "count": 2872, "max": [0.0, 0.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 9911964, "componentType": 5125, "count": 8685, "type": "SCALAR"}], "asset": {"extras": {"author": "thelightning (https://sketchfab.com/thelightning)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/3d-models/ferrari-sf90-stradale-833c5e9fb6f945cc82450fffbb96e77b", "title": "Ferrari SF90 Stradale"}, "generator": "Sketchfab-16.14.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 9946704, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 4345656, "byteOffset": 9946704, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 13035744, "byteOffset": 14292360, "byteStride": 12, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 15664, "byteOffset": 27328104, "byteStride": 16, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 27343768, "uri": "scene.bin"}], "extensionsUsed": ["KHR_materials_clearcoat", "KHR_materials_specular", "KHR_materials_transmission", "KHR_materials_emissive_strength"], "images": [{"uri": "textures/Stradale.006_baseColor.jpeg"}, {"uri": "textures/Stradale.007_baseColor.jpeg"}, {"uri": "textures/Stradale.011_baseColor.png"}, {"uri": "textures/Stradale.011_normal.png"}, {"uri": "textures/Stradale.016_baseColor.jpeg"}, {"uri": "textures/Stradale.019_baseColor.jpeg"}, {"uri": "textures/Stradale.024_baseColor.jpeg"}, {"uri": "textures/Stradale.026_baseColor.jpeg"}], "materials": [{"doubleSided": true, "extensions": {"KHR_materials_clearcoat": {"clearcoatFactor": 1.0, "clearcoatRoughnessFactor": 0.15759458377560343}, "KHR_materials_specular": {"specularColorFactor": [1.0, 1.0, 1.0], "specularFactor": 0.07864545737805206}}, "name": "Stradale.023", "pbrMetallicRoughness": {"baseColorFactor": [0.08125291397129815, 0.08125291397129815, 0.08125291397129815, 1.0], "roughnessFactor": 0.49017085229685975}}, {"doubleSided": true, "name": "Stradale.001", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 0.5045229999999998, 0.005876219999999992, 1.0], "metallicFactor": 0.1272295351611606, "roughnessFactor": 0.417294735622197}}, {"doubleSided": true, "extensions": {"KHR_materials_clearcoat": {"clearcoatFactor": 1.0, "clearcoatRoughnessFactor": 0.04}}, "name": "Stradale.002", "pbrMetallicRoughness": {"baseColorFactor": [0.005355221149492739, 0.005355221149492739, 0.005355221149492739, 1.0], "roughnessFactor": 0.3565646383933113}}, {"doubleSided": true, "name": "Stradale.003", "pbrMetallicRoughness": {"baseColorFactor": [0.01138722854260613, 0.01138722854260613, 0.01138722854260613, 1.0], "roughnessFactor": 0.332272599501757}}, {"doubleSided": true, "extensions": {"KHR_materials_specular": {"specularColorFactor": [1.0, 1.0, 1.0], "specularFactor": 0.0}}, "name": "Stradale.004", "pbrMetallicRoughness": {"baseColorFactor": [0.00996817521196038, 0.00996817521196038, 0.00996817521196038, 1.0], "roughnessFactor": 0.641996095369074}}, {"doubleSided": true, "extensions": {"KHR_materials_specular": {"specularColorFactor": [1.0, 1.0, 1.0], "specularFactor": 0.1818866226671577}}, "name": "Stradale.005", "pbrMetallicRoughness": {"baseColorFactor": [0.02167067078345291, 0.02167067078345291, 0.02167067078345291, 1.0], "roughnessFactor": 0.5448279398028568}}, {"doubleSided": true, "name": "Stradale.006", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.12115652543827202, "roughnessFactor": 0.45980580368241697}}, {"doubleSided": true, "extensions": {"KHR_materials_clearcoat": {"clearcoatFactor": 1.0, "clearcoatRoughnessFactor": 0.04}}, "name": "Stradale.007", "pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "roughnessFactor": 0.5083898814655254}}, {"doubleSided": true, "extensions": {"KHR_materials_specular": {"specularColorFactor": [1.0, 1.0, 1.0], "specularFactor": 0.2365437101731548}}, "name": "Stradale.008", "pbrMetallicRoughness": {"baseColorFactor": [0.00901823, 0.00901823, 0.00901823, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.7634562898268452}}, {"doubleSided": true, "name": "Stradale.009", "pbrMetallicRoughness": {"baseColorFactor": [0.10106008498784341, 0.10106008498784341, 0.10106008498784341, 1.0], "roughnessFactor": 0.0}}, {"alphaMode": "BLEND", "doubleSided": true, "extensions": {"KHR_materials_transmission": {"transmissionFactor": 0.9881576495737222}}, "name": "Stradale.010", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 0.25], "roughnessFactor": 0.0}}, {"doubleSided": true, "extensions": {"KHR_materials_specular": {"specularColorFactor": [1.0, 1.0, 1.0], "specularFactor": 0.21832468100448907}}, "name": "Stradale.011", "normalTexture": {"index": 3}, "pbrMetallicRoughness": {"baseColorTexture": {"index": 2}, "roughnessFactor": 0.502316871742637}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "extensions": {"KHR_materials_emissive_strength": {"emissiveStrength": 9.167207991366741}}, "name": "Stradale.012", "pbrMetallicRoughness": {"metallicFactor": 0.0, "roughnessFactor": 0.8940128534417726}}, {"doubleSided": true, "name": "Stradale.013", "pbrMetallicRoughness": {"baseColorFactor": [0.0196078, 0.0196078, 0.0196078, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.995615562631078}}, {"doubleSided": true, "name": "Stradale.014", "pbrMetallicRoughness": {"baseColorFactor": [0.588, 0.588, 0.588, 1.0], "roughnessFactor": 0.0}}, {"alphaMode": "BLEND", "doubleSided": true, "extensions": {"KHR_materials_transmission": {"transmissionFactor": 0.976011630127945}}, "name": "Stradale.015", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 0.25], "roughnessFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "emissiveTexture": {"index": 4}, "name": "Stradale.016", "pbrMetallicRoughness": {"baseColorTexture": {"index": 4}, "metallicFactor": 0.0, "roughnessFactor": 0.93007462163784}}, {"alphaMode": "BLEND", "doubleSided": true, "extensions": {"KHR_materials_transmission": {"transmissionFactor": 0.9517195912363907}}, "name": "Stradale.017", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 0.0, 0.0, 0.25], "roughnessFactor": 0.0}}, {"doubleSided": true, "emissiveFactor": [1.0, 1.0, 1.0], "extensions": {"KHR_materials_emissive_strength": {"emissiveStrength": 5.705592449320259}}, "name": "Stradale.018", "pbrMetallicRoughness": {"metallicFactor": 0.0, "roughnessFactor": 0.9938464121398224}}, {"doubleSided": true, "name": "Stradale.019", "pbrMetallicRoughness": {"baseColorTexture": {"index": 5}, "metallicFactor": 0.0, "roughnessFactor": 0.8708906052060774}}, {"doubleSided": true, "name": "Stradale.020", "pbrMetallicRoughness": {"baseColorFactor": [0.00722067, 0.00722067, 0.00722067, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9983854091036907}}, {"doubleSided": true, "name": "Stradale.021", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.485253, 0.0, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.8843655580233699}}, {"doubleSided": true, "name": "Stradale.022", "pbrMetallicRoughness": {"baseColorFactor": [0.00792516, 0.00792516, 0.00792516, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.9982278803507437}}, {"doubleSided": true, "name": "<PERSON><PERSON><PERSON>", "pbrMetallicRoughness": {"baseColorFactor": [0.0, 0.0, 0.0, 1.0], "metallicFactor": 0.0}}, {"doubleSided": true, "name": "Stradale.024", "pbrMetallicRoughness": {"baseColorTexture": {"index": 6}, "roughnessFactor": 0.2047393953210972}}, {"doubleSided": true, "name": "Stradale.025", "pbrMetallicRoughness": {"baseColorFactor": [0.588, 0.588, 0.588, 1.0], "roughnessFactor": 0.28368852171864856}}, {"doubleSided": true, "name": "Stradale.026", "pbrMetallicRoughness": {"baseColorTexture": {"index": 7}, "roughnessFactor": 0.4294407550679741}}, {"doubleSided": true, "name": "Stradale.027", "pbrMetallicRoughness": {"baseColorFactor": [0.7656041602732613, 0.5487693389717234, 0.0, 1.0], "roughnessFactor": 0.5691199786944111}}], "meshes": [{"name": "Body.001_Stradale.023_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2}, "indices": 3, "material": 0, "mode": 4}]}, {"name": "Body.001_Stradale.023_0", "primitives": [{"attributes": {"NORMAL": 5, "POSITION": 4, "TEXCOORD_0": 6}, "indices": 7, "material": 0, "mode": 4}]}, {"name": "Body.001_Stradale.001_0", "primitives": [{"attributes": {"NORMAL": 9, "POSITION": 8, "TEXCOORD_0": 10}, "indices": 11, "material": 1, "mode": 4}]}, {"name": "Body.001_Stradale.002_0", "primitives": [{"attributes": {"NORMAL": 13, "POSITION": 12, "TEXCOORD_0": 14}, "indices": 15, "material": 2, "mode": 4}]}, {"name": "Body.002_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 17, "POSITION": 16, "TEXCOORD_0": 18}, "indices": 19, "material": 3, "mode": 4}]}, {"name": "Body.002_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 21, "POSITION": 20, "TEXCOORD_0": 22}, "indices": 23, "material": 3, "mode": 4}]}, {"name": "Body.003_Stradale.004_0", "primitives": [{"attributes": {"NORMAL": 25, "POSITION": 24, "TEXCOORD_0": 26}, "indices": 27, "material": 4, "mode": 4}]}, {"name": "Body.004_Stradale.005_0", "primitives": [{"attributes": {"NORMAL": 29, "POSITION": 28, "TEXCOORD_0": 30}, "indices": 31, "material": 5, "mode": 4}]}, {"name": "Body.005_Stradale.006_0", "primitives": [{"attributes": {"NORMAL": 33, "POSITION": 32, "TEXCOORD_0": 34}, "indices": 35, "material": 6, "mode": 4}]}, {"name": "Body.006_Stradale.007_0", "primitives": [{"attributes": {"NORMAL": 37, "POSITION": 36, "TEXCOORD_0": 38}, "indices": 39, "material": 7, "mode": 4}]}, {"name": "Body.007_Stradale.008_0", "primitives": [{"attributes": {"NORMAL": 41, "POSITION": 40, "TEXCOORD_0": 42}, "indices": 43, "material": 8, "mode": 4}]}, {"name": "Body.008_Stradale.009_0", "primitives": [{"attributes": {"NORMAL": 45, "POSITION": 44, "TEXCOORD_0": 46}, "indices": 47, "material": 9, "mode": 4}]}, {"name": "Body.009_Stradale.010_0", "primitives": [{"attributes": {"NORMAL": 49, "POSITION": 48, "TEXCOORD_0": 50}, "indices": 51, "material": 10, "mode": 4}]}, {"name": "Body.010_Stradale.011_0", "primitives": [{"attributes": {"NORMAL": 53, "POSITION": 52, "TANGENT": 54, "TEXCOORD_0": 55}, "indices": 56, "material": 11, "mode": 4}]}, {"name": "Body.011_Stradale.012_0", "primitives": [{"attributes": {"NORMAL": 58, "POSITION": 57, "TEXCOORD_0": 59}, "indices": 60, "material": 12, "mode": 4}]}, {"name": "Body.012_Stradale.013_0", "primitives": [{"attributes": {"NORMAL": 62, "POSITION": 61, "TEXCOORD_0": 63}, "indices": 64, "material": 13, "mode": 4}]}, {"name": "Body.013_Stradale.014_0", "primitives": [{"attributes": {"NORMAL": 66, "POSITION": 65, "TEXCOORD_0": 67}, "indices": 68, "material": 14, "mode": 4}]}, {"name": "Body.014_Stradale.015_0", "primitives": [{"attributes": {"NORMAL": 70, "POSITION": 69, "TEXCOORD_0": 71}, "indices": 72, "material": 15, "mode": 4}]}, {"name": "Body.015_Stradale.016_0", "primitives": [{"attributes": {"NORMAL": 74, "POSITION": 73, "TEXCOORD_0": 75, "TEXCOORD_1": 76}, "indices": 77, "material": 16, "mode": 4}]}, {"name": "Body.016_Stradale.017_0", "primitives": [{"attributes": {"NORMAL": 79, "POSITION": 78, "TEXCOORD_0": 80}, "indices": 81, "material": 17, "mode": 4}]}, {"name": "Body.017_Stradale.018_0", "primitives": [{"attributes": {"NORMAL": 83, "POSITION": 82, "TEXCOORD_0": 84}, "indices": 85, "material": 18, "mode": 4}]}, {"name": "Body.018_Stradale.019_0", "primitives": [{"attributes": {"NORMAL": 87, "POSITION": 86, "TEXCOORD_0": 88}, "indices": 89, "material": 19, "mode": 4}]}, {"name": "Body.019_Stradale.020_0", "primitives": [{"attributes": {"NORMAL": 91, "POSITION": 90, "TEXCOORD_0": 92}, "indices": 93, "material": 20, "mode": 4}]}, {"name": "Body.019_Stradale.021_0", "primitives": [{"attributes": {"NORMAL": 95, "POSITION": 94, "TEXCOORD_0": 96}, "indices": 97, "material": 21, "mode": 4}]}, {"name": "Body.020_Stradale.022_0", "primitives": [{"attributes": {"NORMAL": 99, "POSITION": 98, "TEXCOORD_0": 100}, "indices": 101, "material": 22, "mode": 4}]}, {"name": "Body.021_Stradale_0", "primitives": [{"attributes": {"NORMAL": 103, "POSITION": 102, "TEXCOORD_0": 104}, "indices": 105, "material": 23, "mode": 4}]}, {"name": "Body.022_Stradale.007_0", "primitives": [{"attributes": {"NORMAL": 107, "POSITION": 106, "TEXCOORD_0": 108}, "indices": 109, "material": 7, "mode": 4}]}, {"name": "Wheel.Bk.L_Stradale.024_0", "primitives": [{"attributes": {"NORMAL": 111, "POSITION": 110, "TEXCOORD_0": 112}, "indices": 113, "material": 24, "mode": 4}]}, {"name": "Wheel.Bk.L_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 115, "POSITION": 114, "TEXCOORD_0": 116}, "indices": 117, "material": 3, "mode": 4}]}, {"name": "Wheel.Bk.L_Stradale.025_0", "primitives": [{"attributes": {"NORMAL": 119, "POSITION": 118, "TEXCOORD_0": 120}, "indices": 121, "material": 25, "mode": 4}]}, {"name": "Wheel.Bk.L_Stradale.006_0", "primitives": [{"attributes": {"NORMAL": 123, "POSITION": 122, "TEXCOORD_0": 124}, "indices": 125, "material": 6, "mode": 4}]}, {"name": "Wheel.Bk.L_Stradale.026_0", "primitives": [{"attributes": {"NORMAL": 127, "POSITION": 126, "TEXCOORD_0": 128}, "indices": 129, "material": 26, "mode": 4}]}, {"name": "Wheel.Bk.R_Stradale.024_0", "primitives": [{"attributes": {"NORMAL": 131, "POSITION": 130, "TEXCOORD_0": 132}, "indices": 133, "material": 24, "mode": 4}]}, {"name": "Wheel.Bk.R_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 135, "POSITION": 134, "TEXCOORD_0": 136}, "indices": 137, "material": 3, "mode": 4}]}, {"name": "Wheel.Bk.R_Stradale.025_0", "primitives": [{"attributes": {"NORMAL": 139, "POSITION": 138, "TEXCOORD_0": 140}, "indices": 141, "material": 25, "mode": 4}]}, {"name": "Wheel.Bk.R_Stradale.006_0", "primitives": [{"attributes": {"NORMAL": 143, "POSITION": 142, "TEXCOORD_0": 144}, "indices": 145, "material": 6, "mode": 4}]}, {"name": "Wheel.Bk.R_Stradale.026_0", "primitives": [{"attributes": {"NORMAL": 147, "POSITION": 146, "TEXCOORD_0": 148}, "indices": 149, "material": 26, "mode": 4}]}, {"name": "Wheel.Ft.L_Stradale.024_0", "primitives": [{"attributes": {"NORMAL": 151, "POSITION": 150, "TEXCOORD_0": 152}, "indices": 153, "material": 24, "mode": 4}]}, {"name": "Wheel.Ft.L_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 155, "POSITION": 154, "TEXCOORD_0": 156}, "indices": 157, "material": 3, "mode": 4}]}, {"name": "Wheel.Ft.L_Stradale.025_0", "primitives": [{"attributes": {"NORMAL": 159, "POSITION": 158, "TEXCOORD_0": 160}, "indices": 161, "material": 25, "mode": 4}]}, {"name": "Wheel.Ft.L_Stradale.006_0", "primitives": [{"attributes": {"NORMAL": 163, "POSITION": 162, "TEXCOORD_0": 164}, "indices": 165, "material": 6, "mode": 4}]}, {"name": "Wheel.Ft.L_Stradale.026_0", "primitives": [{"attributes": {"NORMAL": 167, "POSITION": 166, "TEXCOORD_0": 168}, "indices": 169, "material": 26, "mode": 4}]}, {"name": "Wheel.Ft.R_Stradale.024_0", "primitives": [{"attributes": {"NORMAL": 171, "POSITION": 170, "TEXCOORD_0": 172}, "indices": 173, "material": 24, "mode": 4}]}, {"name": "Wheel.Ft.R_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 175, "POSITION": 174, "TEXCOORD_0": 176}, "indices": 177, "material": 3, "mode": 4}]}, {"name": "Wheel.Ft.R_Stradale.025_0", "primitives": [{"attributes": {"NORMAL": 179, "POSITION": 178, "TEXCOORD_0": 180}, "indices": 181, "material": 25, "mode": 4}]}, {"name": "Wheel.Ft.R_Stradale.006_0", "primitives": [{"attributes": {"NORMAL": 183, "POSITION": 182, "TEXCOORD_0": 184}, "indices": 185, "material": 6, "mode": 4}]}, {"name": "Wheel.Ft.R_Stradale.026_0", "primitives": [{"attributes": {"NORMAL": 187, "POSITION": 186, "TEXCOORD_0": 188}, "indices": 189, "material": 26, "mode": 4}]}, {"name": "Wheelbrake.Bk.L_Stradale.027_0", "primitives": [{"attributes": {"NORMAL": 191, "POSITION": 190, "TEXCOORD_0": 192}, "indices": 193, "material": 27, "mode": 4}]}, {"name": "Wheelbrake.Bk.L_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 195, "POSITION": 194, "TEXCOORD_0": 196}, "indices": 197, "material": 3, "mode": 4}]}, {"name": "Wheelbrake.Bk.R_Stradale.027_0", "primitives": [{"attributes": {"NORMAL": 199, "POSITION": 198, "TEXCOORD_0": 200}, "indices": 201, "material": 27, "mode": 4}]}, {"name": "Wheelbrake.Bk.R_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 203, "POSITION": 202, "TEXCOORD_0": 204}, "indices": 205, "material": 3, "mode": 4}]}, {"name": "Wheelbrake.Ft.L_Stradale.027_0", "primitives": [{"attributes": {"NORMAL": 207, "POSITION": 206, "TEXCOORD_0": 208}, "indices": 209, "material": 27, "mode": 4}]}, {"name": "Wheelbrake.Ft.L_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 211, "POSITION": 210, "TEXCOORD_0": 212}, "indices": 213, "material": 3, "mode": 4}]}, {"name": "Wheelbrake.Ft.R_Stradale.027_0", "primitives": [{"attributes": {"NORMAL": 215, "POSITION": 214, "TEXCOORD_0": 216}, "indices": 217, "material": 27, "mode": 4}]}, {"name": "Wheelbrake.Ft.R_Stradale.003_0", "primitives": [{"attributes": {"NORMAL": 219, "POSITION": 218, "TEXCOORD_0": 220}, "indices": 221, "material": 3, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2], "matrix": [0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.0, 0.009999999776482582, 0.0, 0.0, -0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "726838cdcf524ae9b33b0fd45927823f.fbx"}, {"children": [3, 8, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 46, 48, 50, 52, 58, 64, 70, 76, 79, 82, 85], "name": "RootNode"}, {"children": [4, 5, 6, 7], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.001"}, {"mesh": 0, "name": "Body.001_Stradale.023_0"}, {"mesh": 1, "name": "Body.001_Stradale.023_0"}, {"mesh": 2, "name": "Body.001_Stradale.001_0"}, {"mesh": 3, "name": "Body.001_Stradale.002_0"}, {"children": [9, 10], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.002"}, {"mesh": 4, "name": "Body.002_Stradale.003_0"}, {"mesh": 5, "name": "Body.002_Stradale.003_0"}, {"children": [12], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.003"}, {"mesh": 6, "name": "Body.003_Stradale.004_0"}, {"children": [14], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.004"}, {"mesh": 7, "name": "Body.004_Stradale.005_0"}, {"children": [16], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.005"}, {"mesh": 8, "name": "Body.005_Stradale.006_0"}, {"children": [18], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.006"}, {"mesh": 9, "name": "Body.006_Stradale.007_0"}, {"children": [20], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.007"}, {"mesh": 10, "name": "Body.007_Stradale.008_0"}, {"children": [22], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.008"}, {"mesh": 11, "name": "Body.008_Stradale.009_0"}, {"children": [24], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.009"}, {"mesh": 12, "name": "Body.009_Stradale.010_0"}, {"children": [26], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.010"}, {"mesh": 13, "name": "Body.010_Stradale.011_0"}, {"children": [28], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.011"}, {"mesh": 14, "name": "Body.011_Stradale.012_0"}, {"children": [30], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.012"}, {"mesh": 15, "name": "Body.012_Stradale.013_0"}, {"children": [32], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.013"}, {"mesh": 16, "name": "Body.013_Stradale.014_0"}, {"children": [34], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.014"}, {"mesh": 17, "name": "Body.014_Stradale.015_0"}, {"children": [36], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.015"}, {"mesh": 18, "name": "Body.015_Stradale.016_0"}, {"children": [38], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.016"}, {"mesh": 19, "name": "Body.016_Stradale.017_0"}, {"children": [40], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.017"}, {"mesh": 20, "name": "Body.017_Stradale.018_0"}, {"children": [42], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.018"}, {"mesh": 21, "name": "Body.018_Stradale.019_0"}, {"children": [44, 45], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.019"}, {"mesh": 22, "name": "Body.019_Stradale.020_0"}, {"mesh": 23, "name": "Body.019_Stradale.021_0"}, {"children": [47], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.020"}, {"mesh": 24, "name": "Body.020_Stradale.022_0"}, {"children": [49], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.021"}, {"mesh": 25, "name": "Body.021_Stradale_0"}, {"children": [51], "matrix": [-24.295217514037805, 3.229763151423368e-09, 3.668475782787948e-06, 0.0, 3.6684766285422074e-06, 0.02139327667857523, 24.295204280358238, 0.0, -5.335154087688792e-13, 24.295211909750087, -0.021393283396678234, 0.0, 0.0, 0.11320894956588745, 128.57965087890625, 1.0], "name": "Body.022"}, {"mesh": 26, "name": "Body.022_Stradale.007_0"}, {"children": [53, 54, 55, 56, 57], "matrix": [-24.77245330810518, 3.2935800720477545e-09, 3.7405363828712433e-06, 0.0, 3.9142820841612105e-06, 0.02282672839334786, 25.923099004466028, 0.0, -1.7784971012080927e-13, 25.923118077945258, -0.022826745188605366, 0.0, 80.15817260742188, 31.030595779418945, -0.013602030463516712, 1.0], "name": "Wheel.Bk.L"}, {"mesh": 27, "name": "Wheel.Bk.L_Stradale.024_0"}, {"mesh": 28, "name": "Wheel.Bk.L_Stradale.003_0"}, {"mesh": 29, "name": "Wheel.Bk.L_Stradale.025_0"}, {"mesh": 30, "name": "Wheel.Bk.L_Stradale.006_0"}, {"mesh": 31, "name": "Wheel.Bk.L_Stradale.026_0"}, {"children": [59, 60, 61, 62, 63], "matrix": [24.772453308104765, -5.200611515690327e-09, -5.906216638955599e-06, 0.0, -6.180556906583074e-06, -0.022828994662718732, -25.923099002469915, 0.0, -6.870916354499918e-13, 25.92311807594958, -0.022829011459643694, 0.0, -80.15816497802734, 31.030569076538086, -0.01380467414855957, 1.0], "name": "Wheel.Bk.R"}, {"mesh": 32, "name": "Wheel.Bk.R_Stradale.024_0"}, {"mesh": 33, "name": "Wheel.Bk.R_Stradale.003_0"}, {"mesh": 34, "name": "Wheel.Bk.R_Stradale.025_0"}, {"mesh": 35, "name": "Wheel.Bk.R_Stradale.006_0"}, {"mesh": 36, "name": "Wheel.Bk.R_Stradale.026_0"}, {"children": [65, 66, 67, 68, 69], "matrix": [-24.295217514037805, 3.2301299857176323e-09, 3.668475782787948e-06, 0.0, 3.66847605286174e-06, 0.021393273319523728, 24.295200465662454, 0.0, -1.666811987076816e-13, 24.295211909750087, -0.021393283396678234, 0.0, 78.61400604248047, 29.23499298095703, 227.1209716796875, 1.0], "name": "Wheel.Ft.L"}, {"mesh": 37, "name": "Wheel.Ft.L_Stradale.024_0"}, {"mesh": 38, "name": "Wheel.Ft.L_Stradale.003_0"}, {"mesh": 39, "name": "Wheel.Ft.L_Stradale.025_0"}, {"mesh": 40, "name": "Wheel.Ft.L_Stradale.006_0"}, {"mesh": 41, "name": "Wheel.Ft.L_Stradale.026_0"}, {"children": [71, 72, 73, 74, 75], "matrix": [24.295217514037397, -5.100667000100053e-09, -5.792434691218363e-06, 0.0, -5.792432844244932e-06, -0.021395388875016946, -24.29519092705222, 0.0, -3.998374319364742e-13, 24.295211907879736, -0.021395407351634404, 0.0, -78.61398315429688, 29.23501968383789, 227.12094116210938, 1.0], "name": "Wheel.Ft.R"}, {"mesh": 42, "name": "Wheel.Ft.R_Stradale.024_0"}, {"mesh": 43, "name": "Wheel.Ft.R_Stradale.003_0"}, {"mesh": 44, "name": "Wheel.Ft.R_Stradale.025_0"}, {"mesh": 45, "name": "Wheel.Ft.R_Stradale.006_0"}, {"mesh": 46, "name": "Wheel.Ft.R_Stradale.026_0"}, {"children": [77, 78], "matrix": [-24.295217514037805, 3.2301299857176323e-09, 3.668475782787948e-06, 0.0, 3.66847605286174e-06, 0.021393273319523728, 24.295200465662454, 0.0, -1.666811987076816e-13, 24.295211909750087, -0.021393283396678234, 0.0, 80.15819549560547, 31.030595779418945, -0.013602027669548988, 1.0], "name": "Wheelbrake.Bk.L"}, {"mesh": 47, "name": "Wheelbrake.Bk.L_Stradale.027_0"}, {"mesh": 48, "name": "Wheelbrake.Bk.L_Stradale.003_0"}, {"children": [80, 81], "matrix": [24.295217514018105, -2.123955451032021e-06, 3.1086111911082865e-05, -0.0, -3.108421958153796e-05, 0.021396169535819583, 24.295200463092392, -0.0, 2.1513317083081505e-06, 24.295211907199544, -0.021396179611605955, -0.0, -80.15818786621094, 31.030595779418945, -0.013602027669548988, 1.0], "name": "Wheelbrake.Bk.R"}, {"mesh": 49, "name": "Wheelbrake.Bk.R_Stradale.027_0"}, {"mesh": 50, "name": "Wheelbrake.Bk.R_Stradale.003_0"}, {"children": [83, 84], "matrix": [-24.295217514037805, 3.230162353448836e-09, 3.668475782787948e-06, 0.0, 3.668476628893727e-06, 0.02139327667857523, 24.295204280358238, 0.0, -1.3431349605111461e-13, 24.295215724445875, -0.021393286755729733, 0.0, 78.61400604248047, 29.234996795654297, 227.1209716796875, 1.0], "name": "Wheelbrake.Ft.L"}, {"mesh": 51, "name": "Wheelbrake.Ft.L_Stradale.027_0"}, {"mesh": 52, "name": "Wheelbrake.Ft.L_Stradale.003_0"}, {"children": [86, 87], "matrix": [24.295217514018105, -2.123955451032021e-06, 3.1086111911082865e-05, -0.0, -3.1084214700868564e-05, 0.021396166176313333, 24.295196648396608, -0.0, 2.1513317083081505e-06, 24.295211907199544, -0.021396179611605955, -0.0, -78.6139907836914, 29.234996795654297, 227.1209716796875, 1.0], "name": "Wheelbrake.Ft.R"}, {"mesh": 53, "name": "Wheelbrake.Ft.R_Stradale.027_0"}, {"mesh": 54, "name": "Wheelbrake.Ft.R_Stradale.003_0"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 0, "source": 1}, {"sampler": 0, "source": 2}, {"sampler": 0, "source": 3}, {"sampler": 0, "source": 4}, {"sampler": 0, "source": 5}, {"sampler": 0, "source": 6}, {"sampler": 0, "source": 7}]}