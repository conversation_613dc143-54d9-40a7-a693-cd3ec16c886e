<template>
  <div class="absolute bottom-[1vh] left-[50%] translate-x-[-50%] w-[549px]">
    <div
      class="bg-[url('@/assets/images/bottom-bg.png')] bg-cover bg-no-repeat bg-center w-[549px] h-[34px]"
    ></div>
    <div class="flex">
      <div
        v-for="(item, index) in divs"
        :key="index"
        :class="index === selectedIndex ? 'selected' : 'box'"
        class="h-[32px] bg-no-repeat bg-contain bg-center flex-1 grid place-items-center cursor-pointer"
        @click="selectDiv(item, index)"
      >
        {{ item.text }}
      </div>
    </div>
  </div>
  <Buttom1 v-if="usePageSetting().tab.basement"></Buttom1>
  <Buttom2 v-if="usePageSetting().tab.basement"></Buttom2>
</template>

<script setup>
  import {
    usePageSetting,
    useMainStore,
    useLayerStore,
    useSettingStore,
  } from '@/store/entra/index.js'
  import { onMounted, ref } from 'vue'
  import Buttom1 from '@/components/layout/bottom/bottom-DK/buttom-1.vue'
  import Buttom2 from '@/components/layout/bottom/bottom-DK/buttom-2.vue'
  import { findCSS3DObjectsToVisible } from '@/utils/3d/find.js'
  import { logoutRenderFunc } from '@/utils/3d/animate.js'
  import { revertCar } from '@/blueprint/undergroundParking/car.js'
  import { revertArrow } from '@/blueprint/undergroundParking/arrow.js'
  import { underGroundInit } from '@/core/underGroundInit.js'
  import { manualSelection } from '@/blueprint/addLayer/addModelByLayers.js'
  import { flyTo } from '@/utils/3d/camera.js'
  import { useSidebarVisible } from '@/hooks/useSidebar.js'

  let selectedIndex = ref(-1)

  const divs = [
    { text: '医院总览', id: 'aboutUs' },
    { text: '医院功能', id: 'function' },
    { text: '医院管理', id: 'manage' },
    { text: 'ba系统', id: 'baSystem' },
    { text: '智慧通行', id: 'basement' },
  ]

  function selectDiv(item, index) {
    const pageSetting = usePageSetting()
    selectedIndex.value = index
    let text = item.text
    pageSetting.setTab(item.id, true)
    switchToScene(text)
    //pageSettings的配置改变
  }

  function findDivsById(id, callback) {
    const index = divs.findIndex((div) => div.id === id)
    if (index !== -1) {
      const item = divs[index]
      callback(item, index)
      return item
    } else {
      return null
    }
  }

  onMounted(() => {
    const pageSetting = usePageSetting()
    const tab = pageSetting.tab
    for (const tabKey in tab) {
      const boolean = tab[tabKey]
      //去过有true
      if (boolean) {
        findDivsById(tabKey, function (params, index) {
          selectedIndex.value = index
          lastChoose = params.text
          switchToInit(params.text)
        })
      }
    }
  })
  let lastChoose = null

  function switchToScene(name) {
    const mainStore = useMainStore()
    const mapSetting = useSettingStore()
    let { camera, controls } = mainStore
    //移除上一次选中
    removeLastChoose(lastChoose)
    lastChoose = name
    switch (name) {
      case '医院总览':
        // manualSelection([''])
        manualSelection(['neike', 'waike', 'jingguan', 'jjz', 'fuke', 'menzhen'])
        flyTo(
          camera,
          controls,
          mapSetting.cameraViewOptions.init.camera,
          mapSetting.cameraViewOptions.init.target,
          2000,
          function () {}
        )
        break
      case '医院功能':
        // manualSelection([''])
        manualSelection(['neike', 'waike', 'jingguan', 'jjz', 'fuke', 'menzhen'])
        break
      case '医院管理':
        // manualSelection([''])
        manualSelection(['neike', 'waike', 'jingguan', 'jjz', 'fuke', 'menzhen'])
        break
      case '智慧通行':
        //这里执行树加载的代码
        manualSelection(['diku'])
        //如果初始化选中 就执行true
        if (initIndexName === name) {
          findCSS3DObjectsToVisible(mainStore.bubbles.underGroundInfoCollection, true)
          findCSS3DObjectsToVisible(mainStore.bubbles.underGroundCameraCollection, true)
          useMainStore().underGroundSceneObject.visible = true
        } else {
          switchToInit(name)
        }
        //加一个飞行
        flyTo(
          camera,
          controls,
          mapSetting.undergroundPerspective.positions,
          mapSetting.undergroundPerspective.targets,
          2000,
          function () {}
        )
        break
    }
  }

  function removeLastChoose(name) {
    const mainStore = useMainStore()
    //移除楼层选择
    const element = document.getElementById('floors')
    if (element) {
      // Remove the element from its parent
      element.parentNode.removeChild(element)
    }
    //展开两边数据层
    useSidebarVisible().setVisible(true)

    if (name) {
      switch (name) {
        case '医院总览':
          break
        case '医院功能':
          break
        case '医院管理':
          break
        case '智慧通行':
          useMainStore().underGroundSceneObject.visible = false
          findCSS3DObjectsToVisible(mainStore.bubbles.underGroundInfoCollection, false)
          findCSS3DObjectsToVisible(mainStore.bubbles.underGroundCameraCollection, false)
          let roamElement = document.getElementById('roamId')
          let selectedElements = roamElement?.getElementsByClassName('selected')
          if (selectedElements?.length > 0) {
            let name = selectedElements[0].getElementsByTagName('span')[0].innerHTML
            revertCar(name)
            revertArrow()
          }
          //移除透明的汽车模型
          if (useMainStore().queryCarByName !== '') {
            const car = useMainStore().scene.getObjectByName(useMainStore().queryCarByName)
            if (car) {
              car.material.opacity = 0.3
            }
          }
          break
      }
    }
  }

  let initIndexName

  //初始化时候调用 再次时候就部分区隐藏
  function switchToInit(name) {
    initIndexName = name
    switch (name) {
      case '医院总览':
        //全选树结构
        break
      case '医院功能':
        break
      case '医院管理':
        break
      case '智慧通行':
        //地库初始化
        underGroundInit()
        break
    }
  }
</script>

<style scoped>
  .selected {
    color: #fff;
    background-image: url('@/assets/images/bg-select.png');
  }

  .box {
    color: #fff;
    background-image: url('@/assets/images/bg-noSelect.png');
  }
</style>
