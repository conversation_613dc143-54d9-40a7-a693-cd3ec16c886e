/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/6 09时55分31秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: desc
 */
import * as THREE from "three";

/**
 * 点绘制
 */
export class drawPoint {
    constructor(name, collection) {
        this.material = new THREE.PointsMaterial({
            color: 0xff0000,
            size: 1,
        });
        this.geometry = new THREE.BufferGeometry();
        this.name = name;
        this.collection = collection;
    }

    add(position) {
        let points = new THREE.Points(
            this.geometry.setFromPoints(position),
            this.material,
        );
        points.name = this.name;
        this.collection.add(points);
        return points;
    }

    destroy() {
        this.collection.clear();
    }
}
