/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/9/13 15时59分28秒
 * @LastEditors: du<PERSON><PERSON>hu
 * @Description: desc
 */
import * as THREE from 'three'
import { createApp, ref } from 'vue'
import { useMainStore } from '@/store/modules/mapObject.js'
import { defineComponent } from 'vue'
import simpleInfo from '@/common/simpleInfo.vue'
import { Bubble } from '@/utils/3d/toolTip.js'
import carInfo from '@/components/bubble/underGround/carInfo.vue'

function clickPop(abbreviation, intersects) {
  const mainStore = useMainStore()
  const { bubble, bubbles } = mainStore

  // 摄像机类型与缩写的映射关系
  const cameraTypes = {
    SYBQJK: '拾音半球摄像机',
    DTJK: '电梯摄像机',
    QiuXJK: '球形摄像机',
    QXJK: '枪形摄像机',
    BQCSJK: '半球彩色摄像机',
    RLSBJK: '人脸识别摄像机',
  }

  // 根据英文缩写查找对应的摄像机类型
  // 根据正则表达式提取缩写并查找对应的摄像机类型
  function getCameraType(input) {
    // 正则表达式匹配前面的英文字母部分
    const regex = /^([A-Za-z]+)-\d+[A-Za-z]*-\d+/
    if (input) {
      const match = input.match(regex)

      if (match) {
        const abbreviation = match[1] // 提取到的摄像机缩写
        return cameraTypes[abbreviation] || undefined // 查找缩写对应的摄像机类型
      }
    }
    return undefined // 如果格式不匹配，返回 '无效输入'
  }

  const name = getCameraType(abbreviation)
  console.log(intersects)
  if (name) {
    let vector = intersects[0].point
    vector = vector.clone().add(new THREE.Vector3(0, 5, 0))
    // let vector = new THREE.Vector3(0, 0, 0)
    if (bubble.indoorObject) {
      bubble.indoorObject.destroy()
    }
    bubble.indoorObject = new Bubble(mainStore.bubbles.indoorCollection)

    // 停车场监控定位
    const dom = document.createDocumentFragment()
    // 这里使用 ref 来响应式地管理数据
    const data = ref([
      { name: '设备编号', value: 'B-012' },
      { name: '品牌', value: '佑安' },
      { name: '类型', value: '离子试感烟' },
      { name: '设备状态', value: '在线' },
      { name: '设备周期', value: '2年' },
      { name: '购买时间', value: '2020-11-5' },
      { name: '所在位置', value: '机房1' },
    ])
    const myInfo = ref()
    const visible = ref(true) // 控制 visible 的响应式变量
    const title = ref(name)
    // 处理 visible 状态更新的函数
    const app = createApp({
      render() {
        return h(simpleInfo, {
          data: data,
          myRef: myInfo,
          visible: visible,
          title: title,
          onUpdateVisible: () => {
            app.$el.remove()
            bubble.indoorObject.destroy()
          },
        })
      },
    }).mount(dom)
    const domEls = app.$el
    domEls.style.pointerEvents = 'auto'
    //停车场区域的气泡信息
    bubble.indoorObject.add(
      domEls,
      {
        x: vector.x,
        y: vector.y,
        z: vector.z,
      },
      {
        sx: 0.02,
        sy: 0.02,
      },
      { x: 0, y: Math.PI, z: 0 },
      false
    )
  }
}

export { clickPop }
