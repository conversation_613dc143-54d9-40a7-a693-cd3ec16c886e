<template>
  <div class="w-[100%] h-[250px]">
    <use-charts :options="option" class="h-full w-full"></use-charts>
  </div>
</template>

<script setup name="ManageLeft2">
  import UseCharts from '@/common/useCharts.vue'

  let m2R2Data = [
    {
      value: 252,
      legendname: '特级',
      name: '特级',
      itemStyle: { color: '#8d7fec' },
    },
    {
      value: 168,
      legendname: '一级',
      name: '一级',
      itemStyle: { color: '#5085f2' },
    },
    {
      value: 118,
      legendname: '二级',
      name: '二级',
      itemStyle: { color: '#e75fc3' },
    },
    {
      value: 134,
      legendname: '三级',
      name: '三级',
      itemStyle: { color: '#f87be2' },
    },
    {
      value: 101,
      legendname: '自带',
      name: '自带',
      itemStyle: { color: '#f2719a' },
    },
  ]

  const option = {
    title: [
      {
        text: '合计',
        subtext: 12312 + '个',
        textStyle: {
          fontSize: 14,
          color: '#fff',
        },
        subtextStyle: {
          fontSize: 14,
          color: '#fff',
        },
        textAlign: 'center',
        x: '34.5%',
        y: '25%',
      },
    ],
    tooltip: {
      trigger: 'item',
      formatter: function (parms) {
        let str =
          parms.marker +
          '' +
          parms.data.legendname +
          '</br>' +
          '数量：' +
          parms.data.value +
          '个' +
          '</br>' +
          '占比：' +
          parms.percent +
          '%'
        return str
      },
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      left: '70%',
      align: 'left',
      top: '10%',
      textStyle: {
        color: '#fff',
      },
      height: 250,
    },
    series: [
      {
        name: '标题',
        type: 'pie',
        center: ['35%', '35%'],
        radius: ['45%', '55%'],
        clockwise: false, //饼图的扇区是否是顺时针排布
        avoidLabelOverlap: false,
        label: {
          normal: {
            show: true,
            textStyle: {
              color: '#fff',
            },
            position: 'outter',
            formatter: function (parms) {
              return parms.data.legendname
            },
          },
        },
        labelLine: {
          normal: {
            length: 5,
            length2: 3,
            smooth: true,
          },
        },
        data: m2R2Data,
      },
    ],
  }
</script>

<style scoped></style>
