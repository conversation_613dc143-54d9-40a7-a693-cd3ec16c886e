/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/28 10时28分39秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 模型图层常量
 */
import { defineStore } from 'pinia'
import { nanoid } from 'nanoid'
import { ref } from 'vue'

export const useLayerStore = defineStore({
  id: 'layer',
  state: () => ({
    layer: {
      label: '鄂州医院',
      key: 'eZhouHospital',
      children: [
        {
          label: '土建',
          key: 'tu<PERSON>ian',
          children: [
            {
              label: '景观',
              key: 'landscape',
              children: [
                {
                  label: '景观',
                  url: '/model/tujian/jingguan/landscape.glb',
                  key: 'jingguan',
                },
              ],
            },
            {
              label: '建筑',
              key: 'building',
              children: [
                {
                  label: '地库',
                  url: '/model/tujian/jianzhu/dk.glb',
                  key: 'diku',
                },
                {
                  label: '妇科住院楼',
                  url: '/model/tujian/jianzhu/EZYY_FKZYL_AR.glb',
                  key: 'fuke',
                  floor: 5,
                },
                {
                  label: '门诊医技楼',
                  url: '/model/tujian/jianzhu/EZYY_MZYJL_AR.glb',
                  key: 'menzhen',
                  floor: 4,
                },
                {
                  label: '内科住院楼',
                  url: '/model/tujian/jianzhu/EZYY_NKZYL_AR.glb',
                  key: 'neike',
                  floor: 9,
                },
                {
                  label: '外科住院楼',
                  url: '/model/tujian/jianzhu/EZYY_WKZYL_AR.glb',
                  key: 'waike',
                  floor: 5,
                },
                {
                  label: '急救血站后勤楼',
                  url: '/model/tujian/jianzhu/EZYY_XZJJHQ_AR.glb',
                  key: 'jjz',
                  floor: 6,
                },
                /*  {
                                    label: "氧气汇流排间及消洗中心",
                                    url: "/model/tujian/jianzhu/EZYY_YQHLPJ_AR.glb",
                                    key: "yyq",
                                  },*/
              ],
            },
          ],
        },
        {
          label: '室内',
          key: 'indoor',
          children: [
            {
              label: '妇科住院楼',
              key: 'fuke_indoor',
              floor: 5,
              children: [
                {
                  label: '妇科住院楼5F',
                  url: '/model/shinei/gynecology/gynecology5L.glb',
                  key: 'fuke_5F',
                },
                {
                  label: '妇科住院楼6F',
                  url: '/model/shinei/gynecology/gynecology6L.glb',
                  key: 'fuke_6F',
                },
                {
                  label: '妇科住院楼7F',
                  url: '/model/shinei/gynecology/gynecology7L.glb',
                  key: 'fuke_7F',
                },
                {
                  label: '妇科住院楼8F',
                  url: '/model/shinei/gynecology/gynecology8L.glb',
                  key: 'fuke_8F',
                },
                {
                  label: '妇科住院楼9F',
                  url: '/model/shinei/gynecology/gynecology9L.glb',
                  key: 'fuke_9F',
                },
              ],
            },
            {
              label: '门诊医技楼',
              key: 'menzhen_indoor',
              floor: 4,
              children: [
                {
                  label: '门诊医技楼1F',
                  url: '/model/shinei/outpatient/outpatient1L.glb',
                  key: 'menzhen_1F',
                },
                {
                  label: '门诊医技楼2F',
                  url: '/model/shinei/outpatient/outpatient2L.glb',
                  key: 'menzhen_2F',
                },
                {
                  label: '门诊医技楼3F',
                  url: '/model/shinei/outpatient/outpatient3L.glb',
                  key: 'menzhen_3F',
                },
                {
                  label: '门诊医技楼4F',
                  url: '/model/shinei/outpatient/outpatient4L.glb',
                  key: 'menzhen_4F',
                },
              ],
            },
            {
              label: '内科住院楼',
              key: 'neike_indoor',
              floor: 9,
              children: [
                {
                  label: '内科住院楼1F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment1L.glb',
                  key: 'neike_1F',
                },
                {
                  label: '内科住院楼2F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment2L.glb',
                  key: 'neike_2F',
                },
                {
                  label: '内科住院楼3F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment3L.glb',
                  key: 'neike_3F',
                },
                {
                  label: '内科住院楼4F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment4L.glb',
                  key: 'neike_4F',
                },
                {
                  label: '内科住院楼5F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment5-8L.glb',
                  key: 'neike_5F',
                },
                {
                  label: '内科住院楼6F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment5-8L.glb',
                  key: 'neike_6F',
                },
                {
                  label: '内科住院楼7F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment5-8L.glb',
                  key: 'neike_7F',
                },
                {
                  label: '内科住院楼8F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment5-8L.glb',
                  key: 'neike_8F',
                },
                {
                  label: '内科住院楼9F',
                  url: '/model/shinei/medicalDepartment/medicalDepartment9L.glb',
                  key: 'neike_9F',
                },
              ],
            },
            {
              label: '外科住院楼',
              key: 'waike_indoor',
              floor: 5,
              children: [
                {
                  label: '外科住院楼5F',
                  url: '/model/shinei/surgical/surgical5-7L.glb',
                  key: 'waike_5F',
                },
                {
                  label: '外科住院楼6F',
                  url: '/model/shinei/surgical/surgical5-7L.glb',
                  key: 'waike_6F',
                },
                {
                  label: '外科住院楼7F',
                  url: '/model/shinei/surgical/surgical5-7L.glb',
                  key: 'waike_7F',
                },
                {
                  label: '外科住院楼8F',
                  url: '/model/shinei/surgical/surgical8L.glb',
                  key: 'waike_8F',
                },
                {
                  label: '外科住院楼9F',
                  url: '/model/shinei/surgical/surgical9L.glb',
                  key: 'waike_9F',
                },
              ],
            },
            {
              label: '急救血站后勤楼',
              key: 'jjz_indoor',
              floor: 6,
              children: [
                {
                  label: '急救血站后勤楼1F',
                  url: '/model/shinei/emergency/emergency1L.glb',
                  key: 'jjz_1F',
                },
                {
                  label: '急救血站后勤楼2F',
                  url: '/model/shinei/emergency/emergency2L.glb',
                  key: 'jjz_2F',
                },
                {
                  label: '急救血站后勤楼3F',
                  url: '/model/shinei/emergency/emergency3L.glb',
                  key: 'jjz_3F',
                },
                {
                  label: '急救血站后勤楼4F',
                  url: '/model/shinei/emergency/emergency4L.glb',
                  key: 'jjz_4F',
                },
                {
                  label: '急救血站后勤楼5F',
                  url: '/model/shinei/emergency/emergency5L.glb',
                  key: 'jjz_5F',
                },
                {
                  label: '急救血站后勤楼6F',
                  url: '/model/shinei/emergency/emergency6L.glb',
                  key: 'jjz_6F',
                },
              ],
            },
          ],
        },
      ],
    },
    tree: '',
    checkedKeys: [],
    previousCheckedKeys: [],
  }),
  getters: {},
  //处理业务逻辑
  actions: {},
})
