/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/8/21 10时46分51秒
 * @LastEditors: duhuazhu
 * @Description: desc
 */
//点击楼层的名称 展开楼层数
import { useSidebarVisible } from '@/hooks/useSidebar.js'
import { createApp } from 'vue'
import choose from '@/common/choose.vue'

function showThisFloor(parent, floor) {
  //移除楼层选择
  const element = document.getElementById('floors')
  if (element) {
    // Remove the element from its parent
    element.parentNode.removeChild(element)
  }

  const container = document.createDocumentFragment()
  if (container && container.__vue_app__) {
    // 如果组件已经存在，先卸载并移除
    container.__vue_app__.unmount()
    container.innerHTML = '' // 清空内容
  }
  let child
  if (parent === '外科住院楼' || parent === '妇科住院楼') {
    const startingFloor = 5 // Starting floor number
    child = Array.from({ length: floor }, (_, index) => `${index + startingFloor}F`)
  } else {
    child = Array.from({ length: floor }, (_, index) => `${index + 1}F`)
  }

  //隐藏左右两边的 bar
  useSidebarVisible().setVisible(false)
  //显示楼层数组件
  //引入vue演示 停车区的信息
  const app = createApp(choose, {
    child: child,
    parent: parent,
  }).mount(container)
  const domEls = app.$el
  document.getElementById('layoutM').appendChild(domEls)
  // 将 app 实例保存到 container 上，以便下次判断
  container.__vue_app__ = app
}
export { showThisFloor }
