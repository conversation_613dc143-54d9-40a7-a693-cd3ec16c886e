<template>
  <div ref="chartRef" id="chartId" class="w-full h-full"></div>
</template>
<script setup name="ParkingRight2">
  import * as echarts from 'echarts'
  import { ref, watch, onMounted } from 'vue'
  import { usePageSetting } from '@/store/entra/index.js'
  import request from '@/utils/request.js'
  import { debounce, throttle } from '@/utils/debounce.js'

  const flag = ref(false)
  let data = [
    { name: 'A区', max: 0 },
    { name: 'B区', max: 0 },
    { name: 'C区', max: 0 },
    { name: 'D区', max: 0 },
    { name: 'E区', max: 0 },
  ]
  let value = []
  const option = {
    backgroundColor: '',
    color: ['#3D91F7', '#61BE67'],
    tooltip: {},
    radar: {
      // shape: 'circle',
      center: ['50%', '44%'],
      radius: '70%',
      triggerEvent: true,
      name: {
        textStyle: {
          color: '#fff',
          fontSize: '14',
          borderRadius: 3,
          padding: [3, 5],
        },
      },
      nameGap: '2',
      indicator: data,
      splitArea: {
        areaStyle: {
          color: [
            'rgba(67,142,255, 0.1)',
            'rgba(67,142,255, 0.2)',
            'rgba(67,142,255, 0.4)',
            'rgba(67,142,255, 0.6)',
            'rgba(67,142,255, 0.8)',
            'rgba(67,142,255, 1)',
          ].reverse(),
        },
      },
      // axisLabel:{//展示刻度
      //     show: true
      // },
      axisLine: {
        //指向外圈文本的分隔线样式
        lineStyle: {
          color: 'rgba(0,0,0,0)',
        },
      },
      splitLine: {
        lineStyle: {
          width: 2,
          color: [
            'rgba(67,142,255, 0.1)',
            'rgba(67,142,255, 0.2)',
            'rgba(67,142,255, 0.4)',
            'rgba(67,142,255, 0.6)',
            'rgba(67,142,255, 0.8)',
            'rgba(67,142,255, 1)',
          ].reverse(),
        },
      },
    },
    series: [
      {
        name: '',
        type: 'radar',
        //areaStyle: {normal: {}},
        areaStyle: {
          normal: {
            color: 'rgba(67,98,255, 0.3)',
          },
        },
        symbolSize: 0,
        lineStyle: {
          normal: {
            color: 'rgb(67,98,255)',
            width: 1,
          },
        },
        data: [
          {
            value: value,
            name: '已用车位',
          },
        ],
      },
    ],
  }
  const chartRef = ref(null)
  let chartInstance = null
  onMounted(() => {
    chartInstance = echarts.init(chartRef.value)
    request({
      url: '/hardware/parking/getParkSpaceInfo',
      method: 'get',
      data: {
        parkId: '',
      },
    }).then((request) => {
      let parkingData = request.data.area_info
      parkingData.sort((a, b) => {
        if (a.area_name < b.area_name) {
          return -1
        }
        if (a.area_name > b.area_name) {
          return 1
        }
        return 0
      })
      const transformedArray = parkingData.map((item) => ({
        name: item.foor_name + '区',
        max: item.total_parking_space,
      }))
      data.length = 0
      data.push(...transformedArray)
      const occ = parkingData.map((item) => item.occupied_parking_space)
      value.length = 0
      value.push(...occ)
      chartInstance.setOption(option)
      window.addEventListener('resize', resizeChart)
    })
  })

  onBeforeUnmount(() => {
    if (chartInstance) {
      chartInstance.dispose()
    }
    window.removeEventListener('resize', resizeChart)
  })

  const resizeChart = () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  }
  watch(
    () => usePageSetting().tab.basement,
    (result) => {
      if (result) {
        executionCount++
        // 每次监听到变化时，增加计数器
        // 如果执行次数超过了指定次数（例如1次），直接返回不执行后续操作
        if (executionCount > 1) {
          flag.value = result
          // 重置计数器
          window.executionCount = 0
        }
      }
    },
    { immediate: true }
  )
</script>

<style scoped></style>
