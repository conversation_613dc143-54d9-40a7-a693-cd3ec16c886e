<template>
  <div
      :style="cardStyle"
      class="bg-[url('@/assets/images/pop.png')] bg-transparent bg-cover bg-no-repeat"
  >
    <div class="container">
      <img
          alt="Left Image"
          class="left-image"
          src="@/assets/images/pop-left.png"
      />
      <div class="center-text text-[#fff]">{{ area }}</div>
      <img
          alt="Right Image"
          class="right-image"
          src="@/assets/images/pop-right.png"
      />
    </div>
    <div class="content">
      <div class="left">
        <p>总车位数</p>
        <p class="text-[#69C6FF] text-[28px]">{{ allCars }}</p>
      </div>
      <div class="right">
        <p>剩余车位</p>
        <p class="text-[#11FF94] text-[28px]">{{ lastCars }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import {NCard, NH3, NH4, NH2, NSpace} from "naive-ui";
import {defineComponent} from "vue";

export default {
  props: {
    area: {
      type: Number,
      required: true,
    },
    allCars: {
      type: Number,
      required: true,
    },
    lastCars: {
      type: Number,
      required: true,
    },
  },
  setup(props, {emit}) {
    const cardStyle = {
      width: "300px",
      height: "134px",
    };
    return {
      cardStyle,
    };
  },
};
</script>

<style scoped>
.container {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  width: auto;
  height: 9px;
  margin-top: -10px;
}

.left-image,
.right-image {
  width: 12px; /* Adjust the width as needed */
  height: 9px;
}

.center-text {
  margin: 0 20px; /* Adjust the margin as needed */
  font-size: 24px; /* Adjust the font size as needed */
}

.content {
  width: 300px;
  height: 134px;
  display: flex;
  justify-content: space-around;
}

.left,
.right {
  line-height: 70px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.left p:first-child,
.right p:first-child {
  font-size: 14px;
  color: #e8f5ff;
}
</style>
