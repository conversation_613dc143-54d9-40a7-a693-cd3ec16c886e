<template>
  <div class="container" id="floors">
    <div class="left">{{ parent }}</div>
    <div class="center">
      <ul>
        <li
          v-for="i in child"
          :class="{ highlighted: selectedFloor === i }"
          @click="selectFloor(i)"
          >{{ i }}</li
        >
      </ul>
    </div>
  </div>
</template>

<script setup>
  import { useLayerStore } from '@/store/modules/mapLayer.js'
  import { toRaw } from 'vue'
  import { manualSelection } from '@/blueprint/addLayer/addModelByLayers.js'
  import { flyTo } from '@/utils/3d/camera.js'
  import { useMainStore } from '@/store/modules/mapObject.js'

  const props = defineProps({
    parent: {
      type: String,
      required: true,
    },
    child: {
      type: Array,
      required: true,
    },
  })
  const selectedFloor = ref(null)
  function findByLabel(node, targetLabel) {
    if (node.label === targetLabel) {
      return node
    }

    if (node.children && node.children.length > 0) {
      for (let child of node.children) {
        const result = findByLabel(child, targetLabel)
        if (result) {
          return result
        }
      }
    }

    return null
  }
  function extractNumber(result, str) {
    return result.children.filter((child) => child.label.includes(str))
  }
  function selectFloor(event) {
    selectedFloor.value = event
    const mainStore = useMainStore()
    let { camera, controls } = mainStore
    const layerConfig = useLayerStore()
    let layer = toRaw(layerConfig.layer)
    const indoor = findByLabel(layer, '室内')
    const result = findByLabel(indoor, props.parent)
    const floor = extractNumber(result, event)[0]
    manualSelection([floor.key])
    //定位就选择一下
    let cameraPosition
    let targetPosition
    switch (props.parent) {
      case '内科住院楼':
        cameraPosition = {
          x: -126.94203781609245,
          y: 52.0842686330503,
          z: -51.183826201849094,
        }
        targetPosition = {
          x: -91.57675592953048,
          y: 0.1126370038168965,
          z: 45.6120264398345,
        }
        break
      case '门诊医技楼':
        cameraPosition = {
          x: 160.28528771225297,
          y: 130.5699556936314,
          z: -170.26803914950412,
        }
        targetPosition = {
          x: 115.68435170551219,
          y: -43.9830425029223,
          z: 20.28434376106058,
        }
        break
      case '外科住院楼':
        cameraPosition = {
          x: -17.251264580608712,
          y: 47.325235498571516,
          z: -102.52487835512898,
        }
        targetPosition = {
          x: 14.318962333341828,
          y: -20.198725723414253,
          z: -37.43928298899712,
        }
        break
      case '妇科住院楼':
        cameraPosition = {
          x: 164.79258713961232,
          y: 59.79120650122952,
          z: -111.3976534313289,
        }
        targetPosition = {
          x: 162.1536650854686,
          y: -9.677394050182235,
          z: -48.114956446682164,
        }
        break
      case '急救血站后勤楼':
        cameraPosition = {
          x: -104.27892852107172,
          y: 47.98210866711587,
          z: 63.007213520321585,
        }
        targetPosition = {
          x: -141.27338999913556,
          y: 2.650279246980522,
          z: -23.170673325597694,
        }
        break
    }
    flyTo(camera, controls, cameraPosition, targetPosition, 2000, function () {})
  }
</script>
<style scoped>
  .container {
    position: absolute;
    top: 50%;
    left: 15%;
    transform: translate(-50%, -50%); /* 水平和垂直居中 */
    width: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 410px;
    color: #fff;
  }

  .left {
    width: auto;
    max-width: 100%; /* 确保 div 不会超出其父容器的宽度 */
    word-wrap: break-word;
    height: 30px;
    padding: 5px;
    background-image: url('@/assets/images/left-ba-2-1.png');
    background-size: cover;
    background-position: center;
    margin-right: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .center {
    background-image: url('@/assets/images/left-ba-2-2.png');
    background-size: cover;
    background-position: center;
    flex: 4;
    display: flex;
    align-items: center;
    height: 410px;
  }

  .center ul {
    list-style-type: none;
    padding: 0;
    margin-left: 60px;
  }

  .center li {
    margin: 10px 0;
    cursor: pointer;
    width: 90px;
    height: 30px;
    background-image: url('@/assets/images/left-ba-2-1.png');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .highlighted {
    background-color: #196ac2; /* Change to your desired highlight color */
  }
</style>
