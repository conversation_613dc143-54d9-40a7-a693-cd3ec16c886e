/*
 * @Author: du<PERSON><PERSON><PERSON>
 * @Date: 2024/6/22 11时50分22秒
 * @LastEditors: duhua<PERSON><PERSON>
 * @Description: 页面配置
 */
import { defineStore } from 'pinia'
import { markRaw, ref, reactive } from 'vue'

export const usePageSetting = defineStore({
  id: 'usePageSetting',
  state: () => {
    return {
      tab: {
        aboutUs: true, //医院介绍
        function: false, //医院功能
        manage: false, //医院管理
        baSystem: false, //ba系统 楼宇管理
        basement: false, //智慧通行
      },
    }
  },
  getters: {},
  //处理业务逻辑
  actions: {
    setTab(name, boolean) {
      //第一步 全部设置为false 因为不知道上一次记录的
      for (let key in this.tab) {
        this.tab[key] = false
      }
      //开始设计点击的
      this.tab[name] = boolean
    },
  },
})
