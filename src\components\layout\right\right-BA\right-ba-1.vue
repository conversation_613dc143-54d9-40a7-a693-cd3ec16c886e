<template>
  <use-charts :options="option" class="h-full w-full"></use-charts>
</template>

<style scoped></style>
<script setup name="BARight1">
import * as echarts from "echarts";
import UseCharts from "@/common/useCharts.vue";

let data = [22.6, 78];
const option = {
  backgroundColor: "",
  title: [
    {
      text: "实时温度",
      left: "25%",
      top: "60%",
      textAlign: "center",
      textStyle: {
        fontWeight: "normal",
        fontSize: "12",
        color: "#fff",
        textAlign: "center",
      },
    },
    {
      text: "湿度",
      left: "75%",
      top: "60%",
      textAlign: "center",
      textStyle: {
        fontWeight: "normal",
        fontSize: "12",
        color: "#fff",
        textAlign: "center",
      },
    },
  ],

  //第一个图表
  series: [
    {
      type: "pie",
      hoverAnimation: false, //鼠标经过的特效
      radius: ["55%", "70%"],
      center: ["25%", "40%"],
      startAngle: 225,
      labelLine: {
        normal: {
          show: false,
        },
      },
      label: {
        normal: {
          position: "center",
        },
      },
      data: [
        {
          value: data[0],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#00cefc",
                },
                {
                  offset: 1,
                  color: "#367bec",
                },
              ]),
            },
          },
          label: {
            formatter: "{c}\n℃",
            position: "center",
            show: true,
            textStyle: {
              fontSize: "14",
              fontWeight: "normal",
              color: "#fff",
            },
          },
        },
        {
          value: 8,
          itemStyle: {
            normal: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              color: "rgba(0,0,0,0)",
              borderWidth: 0,
            },
            emphasis: {
              color: "rgba(0,0,0,0)",
              borderWidth: 0,
            },
          },
        },
      ],
    },
    {
      type: "pie",
      hoverAnimation: false, //鼠标经过的特效
      radius: ["55%", "70%"],
      center: ["75%", "40%"],
      startAngle: 225,
      labelLine: {
        normal: {
          show: false,
        },
      },
      label: {
        normal: {
          position: "center",
        },
      },
      data: [
        {
          value: data[1],
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#9FE6B8",
                },
                {
                  offset: 1,
                  color: "#32C5E9",
                },
              ]),
            },
          },
          label: {
            formatter: "{c}%\nRH",
            position: "center",
            show: true,
            textStyle: {
              fontSize: "14",
              fontWeight: "normal",
              color: "#fff",
            },
          },
        },
        {
          value: 35,
          itemStyle: {
            normal: {
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              color: "rgba(0,0,0,0)",
              borderWidth: 0,
            },
            emphasis: {
              color: "rgba(0,0,0,0)",
              borderWidth: 0,
            },
          },
        },
      ],
    },
  ],
};
</script>
