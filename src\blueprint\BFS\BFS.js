/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/3 15时37分51秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: BFS最短路径
 *
 */

// 在矩形中开启BFS
function shortestPathBFS(gridRows, gridCols, start, end, blocked) {
    const directions = [
        [0, 1], // right
        [1, 0], // down
        [0, -1], // left
        [-1, 0], // up
    ];

    const queue = [[...start, []]]; // [x, y, path]
    const visited = new Set();
    visited.add(`${start[0]}-${start[1]}`);

    const blockedSet = new Set(blocked.map(([x, y]) => `${x}-${y}`));

    while (queue.length > 0) {
        const [x, y, path] = queue.shift();

        if (x === end[0] && y === end[1]) {
            return [...path, [x, y]];
        }

        for (const [dx, dy] of directions) {
            const newX = x + dx;
            const newY = y + dy;

            if (
                newX >= 0 &&
                newX < gridRows &&
                newY >= 0 &&
                newY < gridCols &&
                !blockedSet.has(`${newX}-${newY}`) &&
                !visited.has(`${newX}-${newY}`)
            ) {
                queue.push([newX, newY, [...path, [x, y]]]);
                visited.add(`${newX}-${newY}`);
            }
        }
    }

    return []; // if no path found
}

const gridRows = 8; // rows 长
const gridCols = 8; // columns 宽
const start = [0, 0]; //起始位置
const end = [5, 7]; //结束位置
const blocked = [0, 3]; //有阻挡的位置

const path = shortestPathBFS(gridRows, gridCols, start, end, blocked);
if (path.length > 0) {
    console.log("The shortest path is:");
    for (const coord of path) {
        console.log(coord);
    }
} else {
    console.log("No path found");
}
