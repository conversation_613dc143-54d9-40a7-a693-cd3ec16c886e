<template></template>
<style scoped></style>
<script setup>
  import request from '@/utils/request.js'
  onMounted(() => {
    /*  request({
      // url: '/hardware/InfoRelease/call_req', 调用预案
      // url: '/hardware/InfoRelease/status_get', 设备状态查询
      url:'/hardware/InfoRelease/switch_req',  //视频切换
     data:{
       "cmdId": 0,
       "mmpCode": 0,
       "ouputPos": 0,
       srcIp:'123'
     },
      method: 'post'
    }).then((request) =>{
      // let result = request.msg;
      // console.log(result);
      // debugger
    })*/
    request({
      url: '/hardware/energy/getSsulist',
      method: 'get',
    }).then((request) => {
      console.log(request)
    })
  })
</script>
