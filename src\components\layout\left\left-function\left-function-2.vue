<template>
  <!--  1. 监测播放分区的运行状态，故障状态，音量大小。
  2. 监视播放分区的播放音源。
  3．控制播放分区的运行和停止。
  4．调节播放分区的音量大小。
  5．切换播放分区的音源。-->
  <div class="contain-content">
    <table>
      <thead>
      <tr>
        <th>巡查人员</th>
        <th>巡查时间</th>
        <th>巡查地点</th>
      </tr>
      </thead>
      <tbody>
      <tr
          v-for="(item, index) in inspections"
          :key="index"
          :class="{ 'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0 }"
      >
        <td>{{ item.person }}</td>
        <td>{{ item.time }}</td>
        <td>{{ item.location }}</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup name="FunctionLeft2">
import {ref, onMounted} from "vue";

const persons = ["张三", "李四", "王五", "赵六", "孙七"];
const locations = ["急诊室", "病房", "手术室", "放射科", "化验室"];
const inspections = ref([]);

function getRandomItem(arr) {
  return arr[Math.floor(Math.random() * arr.length)];
}

function getRandomDate() {
  const start = new Date(2024, 0, 1);
  const end = new Date(2024, 11, 31);
  const date = new Date(
      start.getTime() + Math.random() * (end.getTime() - start.getTime()),
  );
  return date.toISOString().split("T")[0];
}

function generateInspections() {
  inspections.value.length = 0;
  inspections.value.push(
      ...Array.from({length: 5}, () => ({
        person: getRandomItem(persons),
        time: getRandomDate(),
        location: getRandomItem(locations),
      })),
  );
}

onMounted(() => {
  generateInspections();
});
</script>

<style scoped>
.contain-content {
  width: 100%;
  padding: 0 1vw 3vw 1vw;
  color: #fff;
  overflow: hidden;
}

table {
  width: 100%;
  height: 100%;
}

th {
  background-color: rgba(100, 112, 127, 0.68);
  border: none;
  padding: 0.2vw;
}

td {
  padding: 0.2vw;
  border: none;
}

.even-row {
  background-color: rgba(40, 58, 105, 0.42);
}

.odd-row {
  background-color: rgba(100, 112, 127, 0.85);
}
</style>
