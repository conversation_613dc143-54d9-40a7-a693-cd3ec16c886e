/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/6 10时19分01秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 测试文件 在index中引入
 */
import {useMainStore, useSettingStore} from "@/store/entra/index.js";
import {isDrawing} from "@/blueprint/undergroundParking/testDraw.js";
import {addCar, leaveCar} from "@/blueprint/undergroundParking/car.js";
import {dynamicArrows} from "@/blueprint/undergroundParking/arrow.js";
import {addUnderGroundBubble} from "@/blueprint/undergroundParking/undergroundBubble.js";
import {modelRotation} from "@/blueprint/other/modelRotation.js";
import {logoutRenderFunc} from "@/utils/3d/animate.js";

/**
 * @constructor 智慧漫游初始化
 */
export function underGroundInit() {
    const mapSetting = useSettingStore();
    const mainStore = useMainStore();
    let {gui, guiTest} = mainStore;
    //添加汽车到场景中
    for (let i = 0; i < mapSetting.testCarLeaveLineArray.length; i++) {
        let item = mapSetting.testCarLeaveLineArray[i];
        let carPosition = item.roadLine[0];
        if (carPosition) {
            addCar(item.roadName, carPosition, item.rotationY);
        }
    }
    let objects = {
        isDrawing: guiTest.drawing,
        carRunning: false,
    };
    gui
        .add(objects, "isDrawing")
        .name("是否开启绘制")
        .onChange(function (boolean) {
            guiTest.drawing = boolean;
            isDrawing(boolean);
        });
    gui
        .add(objects, "carRunning")
        .name("汽车运动起来")
        .onChange(function (boolean) {
            if (boolean) {
                logoutRenderFunc("initModelRotation");
                leaveCar();
            }
        });
    //开启气泡组件
    addUnderGroundBubble();
    //鼠标移入 停止旋转
    mainStore.eventDom.addEvent("mousemove", logoutRotation);

    //立即注销
    function logoutRotation() {
        logoutRenderFunc("initModelRotation");
        mainStore.eventDom.removeEvent("mousemove", logoutRotation);
    }
}
