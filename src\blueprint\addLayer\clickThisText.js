/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/8/21 11时43分03秒
 * @LastEditors: du<PERSON><PERSON>hu
 * @Description: desc
 */

import { useMainStore } from '@/store/modules/mapObject.js'
import { genericEvents } from '@/utils/3d/mouse.js'
import { showThisFloor } from '@/core/showThisFloor.js'
import { clickPop } from '@/blueprint/clickPop/clickPop.js'
//点击楼房上的文字

function clickThisText() {
  const mainStore = useMainStore()
  mainStore.eventDom.addEvent('mousedown', function (event) {
    if (event.button === 0) {
      let { firstName, intersects } = genericEvents(event)
      //todo
      console.log(firstName, 'firstName')
      clickPop(firstName, intersects)
      switch (firstName) {
        //门诊
        case 'MZYJL-MZ':
          showThisFloor('门诊医技楼', 4)
          break
        case 'FKZYL':
          showThisFloor('妇科住院楼', 5)
          break
        case 'NKZYL':
          showThisFloor('内科住院楼', 9)
          break
        case 'WKZYL':
          showThisFloor('外科住院楼', 5)
          break
        case 'XZJJHQ':
          showThisFloor('急救血站后勤楼', 6)
          break
        case 'MZYJL-JZ':
          // showThisFloor('急诊楼', 3)
          break
      }
    }
  })
}

export { clickThisText }
