<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="关键字" prop="keyword">
        <el-input v-model="queryParams.keyword" style="width: 240px" placeholder="请输入关键字查询" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="序号" align="center" prop="id" :show-overflow-tooltip="true" />
      <el-table-column label="位置" align="center" prop="addr" :show-overflow-tooltip="true" />
      <el-table-column label="名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="数据名称" align="center" prop="dataName" :show-overflow-tooltip="true" />
      <el-table-column label="在线状态" align="center" prop="onlineStatus" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatOnlineStatus(scope.row.onlineStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="门禁状态" align="center" prop="accessControlStatus" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatAccessControlStatus(scope.row.accessControlStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="门磁报警" align="center" prop="doorMagneticAlarm" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatDoorMagneticAlarm(scope.row.doorMagneticAlarm) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="防拆报警" align="center" prop="tamperAlarm" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatTamperAlarm(scope.row.tamperAlarm) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="读卡器报警" align="center" prop="cardReaderAlarm" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatCardReaderAlarm(scope.row.cardReaderAlarm) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="火警报警" align="center" prop="fireAlarm" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatFireAlarm(scope.row.fireAlarm) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="PassRecord">
import { listGuard } from "@/api/guard";
const { proxy } = getCurrentInstance();

const recordList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dateTime: [],
    keyword: '',
    title: undefined,
    createBy: undefined,
    status: undefined
  },
  rules: {
    title: [{ required: true, message: "公告标题不能为空", trigger: "blur" }],
    type: [{ required: true, message: "公告类型不能为空", trigger: "change" }]
  },
});

const { queryParams, form } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listGuard(queryParams.value).then(({ data: response }) => {
    recordList.value = response.records;
    total.value = response.total;
    loading.value = false;
  });
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    title: undefined,
    type: undefined,
    noticeContent: undefined,
    status: "0"
  };
  proxy.resetForm("noticeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.dateTime = [];
  queryParams.value.keyword = '';
  handleQuery();
}

/** 格式化在线状态 */
function formatOnlineStatus(status) {
  return status === 0 ? '在线' : '离线';
}

/** 格式化门禁状态 */
function formatAccessControlStatus(status) {
  switch (status) {
    case 0:
      return '关闭';
    case 1:
      return '打开';
    case 5:
      return '离线';
    default:
      return status;
  }
}

/** 格式化门磁报警 */
function formatDoorMagneticAlarm(status) {
  switch (status) {
    case 0:
      return '正常';
    case 1:
      return '强行闯入';
    case 2:
      return '超时未关';
    case 5:
      return '离线';
    default:
      return status;
  }
}

/** 格式化防拆报警 */
function formatTamperAlarm(status) {
  switch (status) {
    case 0:
      return '正常';
    case 1:
      return '报警';
    case 5:
      return '离线';
    default:
      return status;
  }
}

/** 格式化读卡器报警 */
function formatCardReaderAlarm(status) {
  switch (status) {
    case 0:
      return '正常';
    case 1:
      return '报警';
    case 5:
      return '离线';
    default:
      return status;
  }
}

/** 格式化火警报警 */
function formatFireAlarm(status) {
  switch (status) {
    case 0:
      return '正常';
    case 1:
      return '报警';
    case 5:
      return '离线';
    default:
      return status;
  }
}

getList();
</script>
