<template>
  <div class="w-[100%] h-[220px]">
    <use-charts :options="option" class="h-full w-full"></use-charts>
  </div>
</template>

<style scoped></style>
<script setup name="FunctionRight2">
  import * as echarts from 'echarts'

  import UseCharts from '@/common/useCharts.vue'

  //
  const option = {
    backgroundColor: '',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
      formatter: function (params) {
        return `${params[0].name}月<br/>
        ${params[0].seriesName}:${params[0].value}次<br/>
        ${params[1].seriesName}:${params[1].value}次`
      },
    },
    legend: {
      itemWidth: 16,
      itemHeight: 16,

      data: [
        {
          name: '进入',
        },
        {
          name: '外出',
        },
      ],
      textStyle: {
        fontSize: 12, //字体大小
        color: '#a4dafe', //字体颜色
      },
      right: '20%', //距离右侧
    },
    grid: {
      left: '5%',
      top: '10px',
      right: '4%',
      bottom: '30%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#273f55',
          },
        },
        axisLabel: {
          // interval: 0, //设置x轴的标签显示可自适应
          show: true,
          textStyle: {
            color: '#fff',
          },
        },
        data: ['0', '1', '2', '3', '4', '5', '6', '7'],
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '次',
        nameTextStyle: {
          //y轴上方单位的颜色
          color: '#fff',
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true, //y轴线
          lineStyle: {
            show: false,
          },
        },
        axisLabel: {
          // margin: 10,
          textStyle: {
            fontSize: 12,
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
          lineStyle: {
            color: ['#56859d'],
            width: 1,
            type: 'solid',
          },
        },
      },
    ],
    series: [
      {
        name: '上周',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 2,
        showSymbol: false,
        lineStyle: {
          normal: {
            width: 2,
            color: '#777779',
          },
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(119,119,121,0.8)',
                },
                {
                  offset: 0.5,
                  color: 'rgba(119,119,121,0.4)',
                },
                {
                  offset: 0.8,
                  color: 'rgba(119,119,121,0.2)',
                },
                {
                  offset: 1,
                  color: 'transparent',
                },
              ],
              false
            ),
            // shadowColor: 'rgba(232,246,254,0.2)',
            shadowBlur: 30,
          },
        },
        itemStyle: {
          normal: {
            color: '#777779',
            borderColor: '#777779',
            borderWidth: 0,
          },
        },

        data: [20, 50, 30, 100, 20, 30, 125, 180],
      },
      {
        name: '本周',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 2,
        showSymbol: false,
        lineStyle: {
          normal: {
            width: 2,
            color: '#32e8f5',
          },
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(50,232,245,0.7)',
                },
                {
                  offset: 0.5,
                  color: 'rgba(50,232,245,0.4)',
                },
                {
                  offset: 0.8,
                  color: 'rgba(50,232,245,0.2)',
                },
                {
                  offset: 1,
                  color: 'transparent',
                },
              ],
              false
            ),
            // shadowColor: 'rgba(232,246,254,0.2)',
            shadowBlur: 30,
          },
        },
        itemStyle: {
          normal: {
            color: '#32e8f5',
            borderColor: '#32e8f5',
            borderWidth: 0,
          },
        },

        data: [20, 50, 120, 50, 40, 50, 155, 210],
      },
    ],
  }
</script>
