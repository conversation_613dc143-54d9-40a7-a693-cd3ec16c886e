/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/6 14时59分03秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 变换
 */
import * as THREE from "three";

/**
 * 绘制路线专用
 * @param coordinateArray  坐标数组
 * @returns {*}  THREE.Vector3数组
 */
function convertToVector3Array(coordinateArray) {
    return coordinateArray.map((coord) => {
        return new THREE.Vector3(coord["0"], coord["1"], coord["2"]);
    });
}

export {convertToVector3Array};
