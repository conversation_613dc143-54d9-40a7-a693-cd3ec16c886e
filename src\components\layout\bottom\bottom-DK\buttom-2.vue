<template>
  <div class="absolute top-[8vh] left-[80%] translate-x-[-50%] w-[449px]">
    <div class="w-[80px] h-[200px]">
      <div class="" id="roamId">
        <div
          v-for="(item, index) in divs"
          :key="index"
          :class="index === selectedIndex ? 'selected' : 'box'"
          class="h-[32px] bg-no-repeat bg-contain bg-center float-left text-center w-[80px] leading-[30px] cursor-pointer"
          @click="selectDiv(index)"
        >
          <span class="box-text">{{ item.text }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useMainStore, useSettingStore } from '@/store/entra/index.js'
  import { flyTo, flyToCoordinate } from '@/utils/3d/camera.js'
  import { ref } from 'vue'

  let selectedIndex = ref(-1)
  const mapSetting = useSettingStore()
  const mainStore = useMainStore()

  const divs = [
    { text: 'A区定位' },
    { text: 'B区定位' },
    { text: 'C区定位' },
    { text: 'D区定位' },
    { text: 'E区定位' },
  ]

  function selectDiv(index) {
    selectedIndex.value = index
    let params = mapSetting.testCarLeaveLineArray[index]
    flyToCoordinate(params.positions, params.targets)
  }
</script>

<style scoped>
  .selected {
    background-image: url('@/assets/images/bottom-dk-select.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: #fff;
  }

  .box {
    background-image: url('@/assets/images/bottom-dk-noSelect.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    color: #fff;
  }
</style>
