{"name": "ruoyi", "version": "3.8.8", "description": "鄂州医院管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@tweenjs/tween.js": "21.0.0", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.0", "axios": "0.28.1", "dplayer": "^1.27.1", "echarts": "5.5.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "2.7.6", "file-saver": "2.0.5", "fuse.js": "6.6.2", "hls.js": "^1.5.13", "html2canvas": "^1.4.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "nanoid": "^5.0.7", "nprogress": "0.2.0", "pinia": "2.1.7", "qs": "^6.12.1", "seemly": "^0.3.8", "three": "^0.155.0", "three.path": "^1.0.1", "unplugin-vue-components": "^0.27.3", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-loading-overlay": "^6.0.4", "vue-router": "4.4.0"}, "devDependencies": {"@vicons/ionicons4": "^0.12.0", "@vitejs/plugin-vue": "5.0.5", "autoprefixer": "^10.4.15", "naive-ui": "^2.38.2", "postcss": "^8.4.29", "sass": "1.77.5", "tailwindcss": "^3.3.3", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-static-copy": "^1.0.6", "vite-plugin-svg-icons": "2.0.1"}}