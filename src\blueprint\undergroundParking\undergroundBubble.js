/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/7 11时45分16秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 停车场气泡标注
 */
//1.先添加一个气泡到场景中
//2.添加相应的信息字段 A-E区 例如 停车场各个分区的总车位数和剩余车位数。
//初始化添加停车场气泡
import { useMainStore, useSettingStore } from '@/store/entra/index.js'
import { Bubble } from '@/utils/3d/toolTip.js'
import { createApp, ref } from 'vue'
import * as THREE from 'three'
import carInfo from '@/components/bubble/underGround/carInfo.vue'
import camera from '@/components/bubble/underGround/camera.vue'
import request from '@/utils/request.js'

/**
 * 添加 地库内所有事件
 */
function addUnderGroundBubble() {
  const mainStore = useMainStore()
  const mapSetting = useSettingStore()
  const { bubble, gui } = mainStore
  let parkingStaticData = mapSetting.parkingStaticData

  //todo
  request({
    url: '/hardware/parking/getParkSpaceInfo',
    method: 'get',
    data: {
      parkId: '',
    },
  }).then((request) => {
    let parkingData = request.data.area_info
    parkingData.sort((a, b) => {
      if (a.area_name < b.area_name) {
        return -1
      }
      if (a.area_name > b.area_name) {
        return 1
      }
      return 0
    })
    for (let i = 0; i < parkingData.length; i++) {
      let vectorInfo = parkingStaticData[i]
      let all = parkingData[i].total_parking_space
      let last = parkingData[i].empty_parking_space
      let area = parkingData[i].area_name
      let { x, z } = vectorInfo.position
      let vector = new THREE.Vector3(x, 10, z)
      //停车场区域的气泡信息
      bubble.underGroundInfoObject = new Bubble(mainStore.bubbles.underGroundInfoCollection)
      //引入vue演示 停车区的信息
      const dom = document.createDocumentFragment()
      const app = createApp(carInfo, {
        area: area + '区',
        allCars: all,
        lastCars: last,
      }).mount(dom)
      const domEls = app.$el
      domEls.style.pointerEvents = 'auto'
      bubble.underGroundInfoObject.add(
        domEls,
        {
          x: vector.x,
          y: vector.y,
          z: vector.z,
        },
        {
          sx: 0.07,
          sy: 0.07,
        },
        null,
        false
      )
    }
    console.log(parkingData)
  })

  //停车场的监控
  bubble.underGroundCameraObject = new Bubble(mainStore.bubbles.underGroundCameraCollection)

  let cameraStaticData = mapSetting.cameraStaticData
  for (let i = 0; i < cameraStaticData.length; i++) {
    let vectorInfo = cameraStaticData[i]
    let { x, z } = vectorInfo.position
    let vector = new THREE.Vector3(x, 0, z)
    // 停车场监控定位
    const dom1 = document.createDocumentFragment()
    const refName = ref(`raf${i + 1}`)
    const app1 = createApp(camera, { refName }).mount(dom1)
    const domEls1 = app1.$el
    domEls1.style.pointerEvents = 'auto'
    const h2Elements = domEls1.getElementsByClassName('cameraText')
    h2Elements[0].textContent = i
    /*    debugger

            // 查找带有 'rounded' 类的元素
            const roundedElement = domEls1.getElementsByClassName('cameraClass')[0];
        debugger
            // 如果元素存在，添加 ID 属性
            if (roundedElement) {
              roundedElement.id = 'far'+i;
            }*/

    bubble.underGroundCameraObject.add(
      domEls1,
      {
        x: vector.x,
        y: vector.y,
        z: vector.z,
      },
      {
        sx: 0.05,
        sy: 0.05,
      },
      null,
      false
    )
  }

  //缩放调整一下
}

export { addUnderGroundBubble }
