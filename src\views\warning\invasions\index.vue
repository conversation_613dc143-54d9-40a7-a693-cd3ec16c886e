<template>
  <div class="app-container flex flex-col gap-4">
    <el-segmented v-model="segmentedValue" :options="warning_page_type" size="large" />
    <el-row :gutter="20">
      <!-- Monitoring Categories -->
      <template v-if="segmentedValue !== '历史报警'">
        <el-col :span="4" :xs="24">
          <div class="head-container flex flex-col gap-2 items-center">
            <el-segmented v-model="deviceStatusValue" :options="monitor_device_status" />
            <el-input v-model="companyName" placeholder="请输入关键字" clearable prefix-icon="Search"
              style="margin-bottom: 20px" />
          </div>
          <div class="head-container">
            <el-tree :data="treeOptions" :props="{ label: 'label', children: 'children' }" :expand-on-click-node="false"
              :filter-node-method="filterNode" ref="treeRef" node-key="id" highlight-current default-expand-all
              @node-click="handleNodeClick" />
          </div>
        </el-col>
      </template>
      <!-- Monitoring List -->
      <el-col :span="segmentedValue !== '历史报警' ? 20 : 24" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
          <!-- Various form items based on segmentedValue -->
          <!-- ... -->
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <template v-if="segmentedValue !== '报警统计'">
          <!-- Real-time and Historical Alarms Table -->
          <el-table v-loading="loading" :data="transformedCameraList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <template v-for="column in tableColumns" :key="column.prop">
              <el-table-column :label="column.label" align="center" :prop="column.prop" :show-overflow-tooltip="true" />
            </template>
            <el-table-column label="事件值" align="center" prop="eventValue" :show-overflow-tooltip="true" />
          </el-table>
        </template>
        <template v-else>
          <!-- Statistics Section -->
          <!-- ... -->
        </template>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="User">
import { ref, reactive, toRefs, watch, getCurrentInstance, computed } from 'vue';
import { useRouter } from 'vue-router';
import LineChart from '@/views/warning/fire-fighting/line-chart';  // 假设你有一个LineChart组件
import { listWarning } from '@/api/warning';
import { translateEvent } from '@/utils';

const router = useRouter();
const { proxy } = getCurrentInstance();
const { monitor_device_status, warning_type, warning_status, warning_model, warning_page_type, warning_range } = proxy.useDict("monitor_device_status", "warning_type", "warning_status", "warning_model", "warning_page_type", "warning_range");

const deviceStatusValue = ref('全部');
const cameraList = ref([]);
const statisticsList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const companyName = ref("");
const treeOptions = ref(undefined);
const dataCycle = ref('年');
// 列表模式
const listModel = ref('列表');
const segmentedValue = ref('实时报警');
// 统计数据
const totalWarnings = ref(0);
const yearOnYear = ref(0);
const monthOnMonth = ref(0);
const safetyAssessment = ref('良好');
// 图表数据
const lineChartData = ref([]);
const statisticsTableData = ref([]);
// 列显隐信息
const listColumns = ref([
  { key: 0, label: '入侵编号', prop: 'intrusionId', visible: true },
  { key: 1, label: '通讯编号', prop: 'commNo', visible: true },
  { key: 2, label: '设备编号', prop: 'devNo', visible: true },
  { key: 3, label: '区域编号', prop: 'zoneNo', visible: true },
  { key: 4, label: '事件名称', prop: 'eventName', visible: true },
  { key: 5, label: '事件类型', prop: 'eventType', visible: true },
  // Removed eventVal column because we will dynamically show it based on eventType
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    daterange: undefined,
    type: undefined,
    model: undefined,
    warningType: undefined,
    dateOfInquiry: undefined,
  },
});

const { queryParams } = toRefs(data);

// 动态生成表格列
const tableColumns = computed(() => {
  const sampleEvent = cameraList.value[0] || {};
  return listColumns.value.filter(column => column.prop in sampleEvent);
});

// Transform the camera list to include eventValue based on eventType
const transformedCameraList = computed(() => {
  return cameraList.value.map(event => ({
    ...event,
    eventType: getEventValue(event)
  }));
});

// Function to get the event value based on eventType
function getEventValue(event) {
  console.log(event);

  switch (event.eventType) {
    case 0:
      return event.eventVal;
    case 1:
      return event.eventNum;
    case 2:
      return event.eventStr;
    default:
      return event.eventVal;
  }
}

// 切换数据周期时的回调
function onChangeDataCycle(value) {
  dataCycle.value = value;
  queryParams.value.dateOfInquiry = undefined;
}

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watch(companyName, val => {
  proxy.$refs["treeRef"].filter(val);
});

/** 查询监控类型下拉树结构 */
function getDeptTree() {
  treeOptions.value = [
    {
      id: 1,
      label: "门诊楼",
      children: [
        {
          id: 2,
          label: "门诊一楼",
        },
        {
          id: 3,
          label: "门诊二楼",
        },
      ],
    },
    {
      id: 4,
      label: "住院部",
      children: [
        {
          id: 5,
          label: "住院部一楼",
        },
        {
          id: 6,
          label: "住院部二楼",
        },
      ],
    },
    {
      id: 7,
      label: "急诊科",
    },
    {
      id: 8,
      label: "手术室",
    },
    {
      id: 9,
      label: "重症监护室",
    },
  ];
}

/** 查询报警信息列表 */
async function getList() {
  try {
    const { data: list } = await listWarning();
    cameraList.value = list.map(translateEvent);
    total.value = cameraList.value.length;
  } catch (e) {
    console.log(e);
  }
}

/** 获取报警统计数据 */
function getStatistics() {
  loading.value = true;
  setTimeout(() => {
    // 生成图表数据
    lineChartData.value = Array.from({ length: 12 }, (v, i) => ({
      date: `2024-${String(i + 1).padStart(2, '0')}`,
      count: Math.floor(Math.random() * 100),
    }));

    // 生成表格数据
    const departments = [
      '门诊一楼', '门诊二楼', '住院部一楼', '住院部二楼', '急诊科', '手术室', '重症监护室'
    ];
    const devices = [
      '红外传感器', '门磁传感器', '震动传感器', '玻璃破碎传感器', '视频监控', '电子围栏', '紧急按钮'
    ];
    statisticsTableData.value = Array.from({ length: 20 }, (v, i) => ({
      zoneNo: `${departments[i % departments.length]}`,
      device: `${devices[i % devices.length]}`,
      count: Math.floor(Math.random() * 100),
      remoteSignal: Math.floor(Math.random() * 50),
      remoteMeasure: Math.floor(Math.random() * 50),
    }));

    // 生成统计数据
    totalWarnings.value = statisticsTableData.value.reduce((acc, curr) => acc + curr.count, 0);
    yearOnYear.value = (Math.random() * 100).toFixed(2);
    monthOnMonth.value = (Math.random() * 100).toFixed(2);
    safetyAssessment.value = ['良好', '一般', '较差'][Math.floor(Math.random() * 3)];

    loading.value = false;
  }, 1000);
}

/** 获取当前页的数据 */
const paginatedCameraList = computed(() => {
  const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
  const end = start + queryParams.value.pageSize;
  return transformedCameraList.value.slice(start, end);
});

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
  getStatistics();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.deptId = undefined;
  proxy.$refs.treeRef.setCurrentKey(null);
  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 跳转至监控详情页面 */
function handleGetCameraInfo(row) {
  router.push({
    path: `/cctv/${row.userId}`,
  });
}

getDeptTree();
getList();
getStatistics();
</script>
