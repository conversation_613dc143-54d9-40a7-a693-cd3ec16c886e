<template>
  <div class="app-container flex flex-col gap-4">
    <el-segmented v-model="segmentedValue" :options="warning_page_type" size="large" />
    <el-row :gutter="20">
      <!--监控分类-->
      <template v-if="segmentedValue !== '历史报警'">
        <el-col :span="4" :xs="24">
          <div class="head-container flex flex-col gap-2 items-center">
            <el-segmented v-model="deviceStatusValue" :options="monitor_device_status" />
            <el-input v-model="companyName" placeholder="请输入关键字" clearable prefix-icon="Search"
              style="margin-bottom: 20px" />
          </div>
          <div class="head-container">
            <el-tree :data="treeOptions" :props="{ label: 'label', children: 'children' }" :expand-on-click-node="false"
              :filter-node-method="filterNode" ref="treeRef" node-key="id" highlight-current default-expand-all
              @node-click="handleNodeClick" />
          </div>
        </el-col>
      </template>
      <!--监控列表-->
      <el-col :span="segmentedValue !== '历史报警' ? 20 : 24" :xs="24">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
          <el-form-item label="日期" prop="daterange" v-if="segmentedValue !== '报警统计'">
            <el-date-picker style="width: 220px;" v-model="queryParams.daterange" type="daterange" range-separator="到"
              start-placeholder="开始时间" end-placeholder="结束时间" />
          </el-form-item>
          <el-form-item label="类型" prop="type" v-if="segmentedValue !== '报警统计'">
            <el-select style="width: 120px" v-model="queryParams.type" placeholder="请选择类型">
              <el-option v-for="it in warning_type" :key="it.value" :value="it.value" :label="it.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报警类型" prop="warningType" v-if="segmentedValue === '实时报警'">
            <el-select style="width: 120px" v-model="queryParams.warningType" placeholder="请选择类型">
              <el-option v-for="it in warning_status" :key="it.value" :value="it.value" :label="it.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="站点" prop="site" v-if="segmentedValue === '历史报警'">
            <el-select style="width: 120px" v-model="queryParams.site" placeholder="请选择站点">
              <el-option v-for="it in sites" :key="it.value" :value="it.value" :label="it.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="级别" prop="level" v-if="segmentedValue === '历史报警'">
            <el-select style="width: 120px" v-model="queryParams.level" placeholder="请选择级别">
              <el-option v-for="it in levels" :key="it.value" :value="it.value" :label="it.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status" v-if="segmentedValue === '历史报警'">
            <el-select style="width: 120px" v-model="queryParams.status" placeholder="请选择状态">
              <el-option v-for="it in statuses" :key="it.value" :value="it.value" :label="it.label"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据周期" prop="dataCycle" v-if="segmentedValue === '报警统计'">
            <el-radio-group v-model="dataCycle" @change="onChangeDataCycle">
              <el-radio v-for="it in warning_range" :key="it.value" :value="it.value">
                {{ it.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="查询日期" prop="dateOfInquiry" v-if="segmentedValue === '报警统计'">
            <el-date-picker style="width: 220px;" v-model="queryParams.dateOfInquiry" :type="dataCycleFormat" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <template v-if="segmentedValue !== '报警统计'">
          <el-row :gutter="10" justify="end" class="mb8" v-if="segmentedValue === '实时报警'">
            <el-col :span="1.5">
              <el-radio-group v-model="listModel">
                <el-radio v-for="it in warning_model" :key="it.value" :value="it.value">
                  {{ it.label }}
                </el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-table v-loading="loading" :data="paginatedCameraList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <template v-for="it in columns" :key="it.prop">
              <el-table-column :label="it.label" align="center" :prop="it.prop" :show-overflow-tooltip="true" />
            </template>
            <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width"
              v-if="segmentedValue === '实时报警'">
              <template #default="scope">
                <el-tooltip content="查看" placement="top">
                  <el-button link type="primary" icon="View" @click="handleGetCameraInfo(scope.row)"
                    v-hasPermi="['video-surveillance:view']"></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template v-else>
          <div class="statistics-header">
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="statistics-item">
                  <span>累计告警：</span><span>{{ totalWarnings }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistics-item">
                  <span>同比：</span><span>{{ yearOnYear }}</span><span>%</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistics-item">
                  <span>环比：</span><span>{{ monthOnMonth }}</span><span>%</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="statistics-item">
                  <span>安全评估：</span><span>{{ safetyAssessment }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
          <div class="statistics-chart">
            <el-card>
              <line-chart :data="lineChartData" />
            </el-card>
          </div>
          <div class="statistics-table">
            <el-table v-loading="loading" :data="statisticsTableData">
              <el-table-column label="站点" prop="siteName" align="center" />
              <el-table-column label="设备" prop="device" align="center" />
              <el-table-column label="数量" prop="count" align="center" />
              <el-table-column label="遥信" prop="remoteSignal" align="center" />
              <el-table-column label="遥测" prop="remoteMeasure" align="center" />
            </el-table>
          </div>
        </template>
        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="User">
import { ref, reactive, toRefs, watch, getCurrentInstance, computed } from 'vue';
import { useRouter } from 'vue-router';
import LineChart from '@/views/warning/fire-fighting/line-chart.vue';  // 假设你有一个LineChart组件

const router = useRouter();
const { proxy } = getCurrentInstance();
const { monitor_device_status, warning_type, warning_status, warning_model, warning_page_type, warning_range } = proxy.useDict("monitor_device_status", "warning_type", "warning_status", "warning_model", "warning_page_type", "warning_range");

const deviceStatusValue = ref('全部');
const cameraList = ref([]);
const statisticsList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref([]);
const companyName = ref("");
const treeOptions = ref(undefined);
const dataCycle = ref('年');
// 列表模式
const listModel = ref('列表');
const segmentedValue = ref('实时报警');
// 统计数据
const totalWarnings = ref(0);
const yearOnYear = ref(0);
const monthOnMonth = ref(0);
const safetyAssessment = ref('良好');
// 图表数据
const lineChartData = ref([]);
const statisticsTableData = ref([]);
// 列显隐信息
const listColumns = ref([
  { key: 0, label: `站点名称`, prop: 'siteName', visible: true },
  { key: 1, label: `告警信息`, prop: 'warningMessage', visible: true },
  { key: 2, label: `事件发生时间`, prop: 'dateOfIncident', visible: true },
  { key: 3, label: `级别`, prop: 'level', visible: true },
  { key: 4, label: `当前值`, prop: 'currentValue', visible: true },
  { key: 5, label: `阀值`, prop: 'threshold', visible: true },
]);
const focusColumns = ref([
  { key: 0, label: `站点`, prop: 'siteName', visible: true },
  { key: 1, label: `设备`, prop: 'device', visible: true },
  { key: 2, label: `类型`, prop: 'type', visible: true },
  { key: 3, label: `时间`, prop: 'dateOfIncident', visible: true },
  { key: 4, label: `等级`, prop: 'level', visible: true },
  { key: 5, label: `数量`, prop: 'count', visible: true },
  { key: 6, label: `最近发生时间`, prop: 'lastIncident', visible: true },
]);
const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    daterange: undefined,
    type: undefined,
    model: undefined,
    warningType: '全部',
    dateOfInquiry: undefined,
  },
});

const { queryParams } = toRefs(data);
// 列表的列
const columns = computed(() => {
  return '列表' == listModel.value ? focusColumns.value : listColumns.value;
});
const dataCycleFormat = computed(() => {
  switch (dataCycle.value) {
    case '年':
      return 'year';
    case '月':
      return 'month';
    case '日':
      return 'dates';
    default:
      return 'year';
  }
});

// 切换数据周期时的回调
function onChangeDataCycle(value) {
  dataCycle.value = value;
  queryParams.value.dateOfInquiry = undefined;
}

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watch(companyName, val => {
  proxy.$refs["treeRef"].filter(val);
});

/** 查询监控类型下拉树结构 */
function getDeptTree() {
  treeOptions.value = [
    {
      id: 1,
      label: "门诊楼",
      children: [
        {
          id: 2,
          label: "门诊一楼",
        },
        {
          id: 3,
          label: "门诊二楼",
        },
      ],
    },
    {
      id: 4,
      label: "住院部",
      children: [
        {
          id: 5,
          label: "住院部一楼",
        },
        {
          id: 6,
          label: "住院部二楼",
        },
      ],
    },
    {
      id: 7,
      label: "急诊科",
    },
    {
      id: 8,
      label: "手术室",
    },
    {
      id: 9,
      label: "重症监护室",
    },
  ];
}

/** 查询报警信息列表 */
function getList() {
  loading.value = true;
  setTimeout(() => {
    const departments = [
      '门诊一楼', '门诊二楼', '住院部一楼', '住院部二楼', '急诊科', '手术室', '重症监护室'
    ];
    const devices = [
      '温度传感器', '湿度传感器', '烟雾传感器', '水浸传感器', '设备监控器', '电力监控器', '门禁系统'
    ];
    const warningMessages = [
      '温度异常', '湿度异常', '烟雾报警', '水浸报警', '设备故障', '断电报警', '门禁异常'
    ];
    cameraList.value = Array.from({ length: 50 }, (v, i) => ({
      userId: `${i + 1}`,
      siteName: `${departments[i % departments.length]}`,
      device: `${devices[i % devices.length]}`,
      type: `${warningMessages[i % warningMessages.length]}`,
      count: Math.floor(Math.random() * 100),
      dateOfIncident: `2024-08-${String(i % 30 + 1).padStart(2, '0')} 10:00:00`,
      lastIncident: `2024-08-${String(i % 30 + 1).padStart(2, '0')} 10:00:00`,
      level: ['高', '中', '低'][i % 3],
      currentValue: `${(Math.random() * 100).toFixed(2)}`,
      threshold: `${(Math.random() * 100).toFixed(2)}`,
    }));
    total.value = cameraList.value.length;
    loading.value = false;
  }, 1000);
}

/** 获取报警统计数据 */
function getStatistics() {
  loading.value = true;
  setTimeout(() => {
    // 生成图表数据
    lineChartData.value = Array.from({ length: 12 }, (v, i) => ({
      date: `2024-${String(i + 1).padStart(2, '0')}`,
      count: Math.floor(Math.random() * 100),
    }));

    // 生成表格数据
    const departments = [
      '门诊一楼', '门诊二楼', '住院部一楼', '住院部二楼', '急诊科', '手术室', '重症监护室'
    ];
    const devices = [
      '温度传感器', '湿度传感器', '烟雾传感器', '水浸传感器', '设备监控器', '电力监控器', '门禁系统'
    ];
    statisticsTableData.value = Array.from({ length: 20 }, (v, i) => ({
      siteName: `${departments[i % departments.length]}`,
      device: `${devices[i % devices.length]}`,
      count: Math.floor(Math.random() * 100),
      remoteSignal: Math.floor(Math.random() * 50),
      remoteMeasure: Math.floor(Math.random() * 50),
    }));

    // 生成统计数据
    totalWarnings.value = statisticsTableData.value.reduce((acc, curr) => acc + curr.count, 0);
    yearOnYear.value = (Math.random() * 100).toFixed(2);
    monthOnMonth.value = (Math.random() * 100).toFixed(2);
    safetyAssessment.value = ['良好', '一般', '较差'][Math.floor(Math.random() * 3)];

    loading.value = false;
  }, 1000);
}

/** 获取当前页的数据 */
const paginatedCameraList = computed(() => {
  const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
  const end = start + queryParams.value.pageSize;
  return cameraList.value.slice(start, end).filter(item => {
    const matchesDept = queryParams.value.deptId ? item.siteName.includes(getDeptNameById(queryParams.value.deptId)) : true;
    const matchesName = queryParams.value.userName ? item.device.includes(queryParams.value.userName) : true;
    const matchesDate = queryParams.value.daterange ? (new Date(item.dateOfIncident) >= new Date(queryParams.value.daterange[0]) && new Date(item.dateOfIncident) <= new Date(queryParams.value.daterange[1])) : true;
    const matchesType = queryParams.value.type ? item.type.includes(queryParams.value.type) : true;
    const matchesWarningType = queryParams.value.warningType === '全部' ? true : item.type.includes(queryParams.value.warningType);
    return matchesDept && matchesName && matchesDate && matchesType && matchesWarningType;
  });
});

/** 根据部门ID获取部门名称 */
function getDeptNameById(id) {
  const findDept = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) return node.label;
      if (node.children) {
        const result = findDept(node.children, id);
        if (result) return result;
      }
    }
    return null
    return null;
  };
  return findDept(treeOptions.value, id);
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id;
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.deptId = undefined;
  queryParams.value.daterange = undefined;
  queryParams.value.type = undefined;
  queryParams.value.warningType = '全部';
  proxy.$refs.treeRef.setCurrentKey(null);
  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 跳转至监控详情页面 */
function handleGetCameraInfo(row) {
  router.push({
    path: `/ cctv / ${row.userId} `,
  });
}

getDeptTree();
getList();
getStatistics();
</script>

<style scoped>
.statistics-header {
  margin-bottom: 20px;
}

.statistics-item {
  font-size: 16px;
  font-weight: bold;
}

.statistics-chart,
.statistics-table {
  margin-top: 20px;
}
</style>
