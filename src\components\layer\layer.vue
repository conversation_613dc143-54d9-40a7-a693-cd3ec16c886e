<template>
  <div
    v-show="visible"
    class="bg-[url('@/assets/images/layer-bg.png')] bg-cover bg-no-repeat bg-center tree-container p-[10px]"
    style="
      min-width: 340px;
      max-height: 400px;
      position: absolute;
      right: 0;
      top: 5vh;
      z-index: 109;
    "
  >
    <div
      class="text-[16px] float-left ml-[20px] mb-[15px] text-[#fff]"
      style="text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25)"
    >
      图层
    </div>
    <n-scrollbar style="max-height: 320px">
      <n-tree
        ref="tree"
        :accordion="true"
        :data="data"
        :checked-keys="checkedKeys"
        :default-checked-keys="defaultCheckedKeys"
        :default-expanded-keys="defaultExpandedKeys"
        :selectable="false"
        block-line
        cascade
        checkable
        @update:checked-keys="updateCheckedKeys"
      />
    </n-scrollbar>
  </div>
</template>
<script setup>
  import { ref, onMounted } from 'vue'
  import { useLayerStore, useMainStore, useSettingStore } from '@/store/entra/index.js'
  import { IosClose } from '@vicons/ionicons4'
  import {
    createData,
    findNodesByIds,
    getAllNodes,
    updateCheckedKeys,
  } from '@/blueprint/addLayer/addModelByLayers.js'
  import { NButton, NIcon } from 'naive-ui'
  import { searchOnModel } from '@/utils/3d/camera.js'
  import Buttom from '@/components/layout/bottom/buttom.vue'

  const mainStore = useMainStore()
  const mapSetting = useSettingStore()
  /*const defaultCheckedKeys = ref([
  "fuke",
  "menzhen",
  "neike",
  "waike",
  "jjz",
  "yyq",
]);*/
  const defaultCheckedKeys = ref(['neike', 'waike', 'jingguan', 'jjz', 'fuke', 'menzhen'])
  const checkedKeys = ref(['neike', 'waike', 'jingguan', 'jjz', 'fuke', 'menzhen'])
  // const defaultCheckedKeys = ref([''])
  // const checkedKeys = ref([''])
  const defaultExpandedKeys = ref(['eZhouHospital', 'tuJian', 'building'])

  useLayerStore().checkedKeys = checkedKeys

  const tree = ref('')
  useLayerStore().tree = tree

  const visible = mapSetting.getLayerShow
  const data = createData()
  onMounted(() => {
    const treeNode = findNodesByIds(data, defaultCheckedKeys.value)
    const newArray = []
    getAllNodes(treeNode, newArray)
    const ikeys = newArray.map((item) => item.key)
    let meta = {
      action: 'check',
      node: newArray,
    }
    updateCheckedKeys(ikeys, newArray, meta)
  })
</script>

<style scoped>
  .tree-container :deep(.n-divider) {
    color: #fff;
  }

  .tree-container :deep(.n-tree .n-tree-node-content) {
    color: #fff;
  }

  .tree-container :deep(.n-tree.n-tree--block-line .n-tree-node:not(.n-tree-node--disabled):hover) {
    background: transparent !important;
  }
</style>
