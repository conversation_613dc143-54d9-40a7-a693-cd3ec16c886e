<template>
  <simple-info :data="data[0]" title="设备信息" :visible="visible"></simple-info>
</template>

<style scoped>

</style>
<script setup>
import request from "@/utils/request.js";
import SimpleInfo from "@/common/simpleInfo.vue";

const data = ref([]);
const visible = ref(true);
const translations = {
  fieldNames: {
    id: '编号',
    addr: '位置',
    name: '名称',
    dataName: '数据名称',
    onlineStatus: '在线状态',
    accessControlStatus: '门禁状态',
    doorMagneticAlarm: '门磁报警',
    tamperAlarm: '防拆报警',
    cardReaderAlarm: '读头防剪报警',
    fireAlarm: '火警报警'
  },
  fieldValues: {
    onlineStatus: {
      0: '离线',
      1: '在线'
    },
    accessControlStatus: {
      0: '关闭',
      1: '打开',
      5: '离线'
    },
    doorMagneticAlarm: {
      0: '正常',
      1: '强行闯入',
      2: '超时未关',
      5: '离线'
    },
    tamperAlarm: {
      0: '正常',
      1: '报警',
      5: '离线'
    },
    cardReaderAlarm: {
      0: '正常',
      1: '报警',
      5: '离线'
    },
    fireAlarm: {
      0: '正常',
      1: '报警',
      5: '离线'
    }
  }
};

function translateData(data) {
  return data.map(item => {
    const translatedItem = {};

    for (const key in item) {
      const translatedKey = translations.fieldNames[key] || key;
      const translatedValue = translations.fieldValues[key]
          ? translations.fieldValues[key][item[key]]
          : item[key];

      translatedItem[translatedKey] = translatedValue;
    }

    return translatedItem;
  });
}


onMounted(() => {
  request({
    url: '/hardware/pkAccessControl',
    method: 'get'
  }).then((request) => {
    let result = request.data.records;
    let array = [];
    data.value.length = 0;
    let resultTranslated = translateData(result);
    for (let i = 0; i < resultTranslated.length; i++) {
      array.push(...Object.entries(resultTranslated[i]).map(([name, value]) => ({name, value})));
      data.value.push([...array])
    }
  })
})

</script>
