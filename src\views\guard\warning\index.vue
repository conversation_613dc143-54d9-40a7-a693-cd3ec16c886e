<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="告警类型" prop="warningType">
        <el-input v-model="queryParams.warningType" style="width: 200px" placeholder="请输入告警类型" clearable />
      </el-form-item>
      <el-form-item label="报警类型" prop="reserved">
        <el-select v-model="queryParams.reserved" placeholder="请选择报警类型" clearable style="width: 200px">
          <el-option label="非法打开报警或未关闭" value="0" />
          <el-option label="撤销报警" value="1" />
          <el-option label="防撬报警" value="2" />
          <el-option label="门未关闭" value="3" />
          <el-option label="非法打开" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="告警时间" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="warningList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="告警时间" align="center" prop="warningDateTime" width="160" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.warningDateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="告警类型" align="center" prop="warningType" width="120" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.warningType">{{ scope.row.warningType }}</span>
          <span v-else class="text-gray-400">未知</span>
        </template>
      </el-table-column>
      <el-table-column label="报警类型" align="center" prop="reserved" width="150" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag :type="getWarningTypeTagType(scope.row.reserved)">
            {{ formatWarningType(scope.row.reserved) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="告警备注" align="center" prop="warningMemo" width="200" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.warningMemo">{{ scope.row.warningMemo }}</span>
          <span v-else class="text-gray-400">无</span>
        </template>
      </el-table-column>
      <el-table-column label="修改人" align="center" prop="modifiedUser" width="100" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.modifiedUser">{{ scope.row.modifiedUser }}</span>
          <span v-else class="text-gray-400">--</span>
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center" prop="modifiedDateTime" width="160" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.modifiedDateTime">{{ formatDateTime(scope.row.modifiedDateTime) }}</span>
          <span v-else class="text-gray-400">--</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination 
      v-show="total > 0" 
      :total="total" 
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" 
      @pagination="getList" 
    />

    <!-- 告警详情对话框 -->
    <el-dialog title="告警详情" v-model="detailVisible" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="告警时间" :span="2">
          {{ formatDateTime(currentWarning.warningDateTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="告警类型">{{ currentWarning.warningType || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="报警类型">
          <el-tag :type="getWarningTypeTagType(currentWarning.reserved)">
            {{ formatWarningType(currentWarning.reserved) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="告警备注" :span="2">
          {{ currentWarning.warningMemo || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="修改人">{{ currentWarning.modifiedUser || '--' }}</el-descriptions-item>
        <el-descriptions-item label="修改时间">
          {{ currentWarning.modifiedDateTime ? formatDateTime(currentWarning.modifiedDateTime) : '--' }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GuardWarning">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { listGuardWarning } from "@/api/guard";
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();

const warningList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const detailVisible = ref(false);
const currentWarning = ref({});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    warningType: undefined,
    reserved: undefined,
    dateRange: []
  }
});

const { queryParams } = toRefs(data);

/** 查询门禁告警列表 */
function getList() {
  loading.value = true;
  
  // 处理时间范围参数
  const params = { ...queryParams.value };
  if (params.dateRange && params.dateRange.length === 2) {
    params.startTime = params.dateRange[0];
    params.endTime = params.dateRange[1];
  }
  delete params.dateRange;
  
  listGuardWarning(params).then(response => {
    // 处理分页数据结构
    if (response.data && response.data.records && Array.isArray(response.data.records)) {
      warningList.value = response.data.records;
      total.value = response.data.total || 0;
    } else if (response.data && Array.isArray(response.data)) {
      // 兼容直接返回数组的情况
      warningList.value = response.data;
      total.value = response.data.length;
    } else {
      warningList.value = [];
      total.value = 0;
    }
    loading.value = false;
  }).catch(error => {
    loading.value = false;
    console.error("获取门禁告警列表失败:", error);
    ElMessage.error("获取门禁告警列表失败");
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.dateRange = [];
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  // 处理多选逻辑
}

/** 查看告警详情 */
function handleView(row) {
  currentWarning.value = { ...row };
  detailVisible.value = true;
}

/** 格式化日期时间 */
function formatDateTime(dateTime) {
  if (!dateTime) return '';
  return parseTime(dateTime, '{y}-{m}-{d} {h}:{i}:{s}');
}

/** 格式化报警类型 */
function formatWarningType(type) {
  switch (type) {
    case '0':
      return '非法打开报警或未关闭';
    case '1':
      return '撤销报警';
    case '2':
      return '防撬报警';
    case '3':
      return '门未关闭';
    case '4':
      return '非法打开';
    default:
      return type || '未知';
  }
}

/** 获取报警类型标签颜色 */
function getWarningTypeTagType(type) {
  switch (type) {
    case '0':
    case '4':
      return 'danger';  // 非法打开相关 - 红色
    case '1':
      return 'success'; // 撤销报警 - 绿色
    case '2':
      return 'warning'; // 防撬报警 - 橙色
    case '3':
      return 'info';    // 门未关闭 - 蓝色
    default:
      return '';        // 默认颜色
  }
}

// 初始化数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.text-gray-400 {
  color: #9ca3af;
}
</style>
