/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/18 11时32分39秒
 * @LastEditors: du<PERSON><PERSON>hu
 * @Description: 交互事件
 */
/**
 * @Description:交互事件 事件流
 */
export class BomEvent {
    constructor(element) {
        this.element = element;
        this.elementArray = [];
        this.defaultEvent = [
            "click",
            "mouseenter",
            "mousemove",
            "mouseleave",
            "mouseup",
            "dblclick",
            "contextmenu",
            "mouseover",
            "mouseout",
            "mousedown",
        ];
    }

    addEvent(type, handle) {
        if (this.element.addEventListener) {
            //不存在 再添加
            if (!this.checkEvent(type)) {
                if (this.defaultEvent.includes(type)) {
                    this.element.addEventListener(type, handle, false);
                } else {
                    //其他自定义
                    handle(this.element);
                }
                this.elementArray.push({
                    type: type,
                    Fn: handle,
                });
            }
        }
    }

    removeEvent(type, handle) {
        if (this.element.removeEventListener && this.defaultEvent.includes(type)) {
            this.element.removeEventListener(type, handle, false);
        } else {
            //右键双击
            this.element.removeEventListener("mousedown", handle, false);
        }
        for (let i = this.elementArray.length - 1; i > 0; i--) {
            if (this.elementArray[i].type === type) {
                this.elementArray.splice(i, 1);
            }
        }
    }

    checkEvent(type) {
        return this.elementArray.some(function (obj) {
            return obj.type === type;
        });
    }

    //移除所有
    destroy(type) {
        for (let i = this.elementArray.length - 1; i > 0; i--) {
            if (this.elementArray[i].type === type) {
                this.removeEvent(type, this.elementArray[i].Fn);
            }
        }
    }
}
