<template>
  <div class="left-1">
    <div class="left-row-1">
      <div class="left-box-1">
        <left1Item des="园区总车位" :number=totalParkingSpaceSum title="车"></left1Item>
      </div>
      <div class="left-box-1">
        <left1Item des="园区剩余车位" :number=emptyParkingSpaceSum title="车"></left1Item>
      </div>
    </div>
    <div class="left-row-1">
      <div class="left-box-1">
        <left1Item des="紧急告警数" number="205" title="警"></left1Item>
      </div>
      <div class="left-box-1">
        <left1Item des="异常设备数量" number="205" title="设"></left1Item>
      </div>
    </div>
  </div>
</template>
<script setup name="ParkingLeft1">
import left1Item from "@/components/layout/left/left-DK/left-1-item.vue";
import request from "@/utils/request.js";
import * as THREE from "three";
import {Bubble} from "@/utils/3d/toolTip.js";
import {createApp} from "vue";
import carInfo from "@/components/bubble/underGround/carInfo.vue";

let totalParkingSpaceSum = ref(0);
let emptyParkingSpaceSum = ref(0);

request({
  url: '/hardware/parking/getParkSpaceInfo',
  method: 'get',
  data: {
    parkId: ''
  },
}).then((request) => {
  let parkingData = request.data.area_info;
  parkingData.forEach(parkingArea => {
    totalParkingSpaceSum.value += parkingArea.total_parking_space;
    emptyParkingSpaceSum.value += parkingArea.empty_parking_space;
  });
})

</script>

<style scoped>
.left-1 {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.left-row-1 {
  width: 100%;
}

.left-box-1 {
  flex: 1;
  text-align: center;
  margin-bottom: 1vh;
}

.left-box-1:last-child {
  margin-right: 0;
}

.left-row-1:last-child {
  margin-bottom: 0;
}

.left-row-1:first-child {
  margin-top: 0;
}
</style>
