@font-face {
    font-family: "iconfont"; /* Project id 4590623 */
    src: url('iconfont.woff2?t=1718763164085') format('woff2'),
    url('iconfont.woff?t=1718763164085') format('woff'),
    url('iconfont.ttf?t=1718763164085') format('truetype');
}

.iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-4:before {
    content: "\e600";
}

.icon-3:before {
    content: "\e601";
}

.icon-2:before {
    content: "\e602";
}

.icon-icon-test:before {
    content: "\e60d";
}

