<template>
  <div
    v-if="visible"
    class="absolute group top-[4vh] right-0 pt-[15px] pr-[30px] pl-[30px] origin-left w-[27vw] h-[76vh] bg-cover bg-no-repeat bg-center scale-100 z-[50] duration-500 bg-[url('@/assets/images/right.png')]"
  >
    <div class="w-full h-1/3">
      <my-title v-if="pageSettings.tab.basement" title="空余车位"></my-title>
      <rightDKParking v-if="pageSettings.tab.basement"></rightDKParking>

      <div v-if="pageSettings.tab.aboutUs">
        <my-title title="消防设备告警统计"></my-title>
      </div>
      <rightAboutFire v-if="pageSettings.tab.aboutUs"></rightAboutFire>

      <div v-if="pageSettings.tab.manage">
        <my-title title="收入分类"></my-title>
      </div>
      <rightManageIncome v-if="pageSettings.tab.manage"></rightManageIncome>

      <div v-if="pageSettings.tab.function">
        <my-title title="入侵报警"></my-title>
      </div>
      <rightFunctionInvasion v-if="pageSettings.tab.function"></rightFunctionInvasion>

      <div v-if="pageSettings.tab.baSystem">
        <my-title title="环境监控数据"></my-title>
      </div>
      <rightBAEnvironment v-if="pageSettings.tab.baSystem"></rightBAEnvironment>
    </div>
    <div class="w-full h-1/3">
      <div v-if="pageSettings.tab.basement">
        <my-title title="各区车位使用情况"></my-title>
      </div>
      <rightDKUsage v-if="pageSettings.tab.basement"></rightDKUsage>

      <div v-if="pageSettings.tab.aboutUs">
        <my-title title="人员统计"></my-title>
      </div>
      <rightAboutPeople v-if="pageSettings.tab.aboutUs"></rightAboutPeople>

      <div v-if="pageSettings.tab.manage">
        <my-title title="本年度质量评分"></my-title>
      </div>
      <rightManageQuality v-if="pageSettings.tab.manage"></rightManageQuality>

      <div v-if="pageSettings.tab.function">
        <my-title title="出入口门禁"></my-title>
      </div>
      <rightFunctionAccess v-if="pageSettings.tab.function"></rightFunctionAccess>

      <div v-if="pageSettings.tab.baSystem">
        <my-title title="空调设备状态统计"></my-title>
      </div>
      <rightBAACStatus v-if="pageSettings.tab.baSystem"></rightBAACStatus>
    </div>
    <div class="w-full h-1/3">
      <div v-if="pageSettings.tab.basement">
        <my-title title="告警事件列表"></my-title>
        <rightDKAlarms></rightDKAlarms>
      </div>
      <div v-if="pageSettings.tab.aboutUs">
        <my-title title="安防统计"></my-title>
      </div>
      <rightAboutSecurity v-if="pageSettings.tab.aboutUs"></rightAboutSecurity>

      <div v-if="pageSettings.tab.manage">
        <my-title title="科室费用数据统计"></my-title>
      </div>
      <rightManageDept v-if="pageSettings.tab.manage"></rightManageDept>

      <div v-if="pageSettings.tab.function">
        <my-title title="多功能会议"></my-title>
      </div>
      <rightFunctionMeeting v-if="pageSettings.tab.function"></rightFunctionMeeting>

      <div v-if="pageSettings.tab.baSystem">
        <my-title title="空调设备工况"></my-title>
      </div>
      <rightBAACOperation v-if="pageSettings.tab.baSystem"></rightBAACOperation>
    </div>
  </div>
  <div
    class="bg-[url('@/assets/images/rightBar.png')] bg-cover bg-center bg-no-repeat w-[27px] absolute right-0 h-full top-0 z-[100] cursor-pointer"
    @click="onToggleSidebar"
  ></div>
</template>
<script setup>
  import { useSidebarVisible } from '@/hooks/useSidebar.js'
  import MyTitle from '@/common/title.vue'
  import { usePageSetting } from '@/store/modules/pageSetting'
  import rightDKParking from '@/components/layout/right/right-DK/right-dk-1.vue'
  import rightDKUsage from '@/components/layout/right/right-DK/right-dk-2.vue'
  import rightDKAlarms from '@/components/layout/right/right-DK/right-dk-3.vue'

  import rightAboutFire from '@/components/layout/right/right-About/right-1.vue'
  import rightAboutPeople from '@/components/layout/right/right-About/right-2.vue'
  import rightAboutSecurity from '@/components/layout/right/right-About/right-3.vue'

  import rightManageIncome from '@/components/layout/right/right-manage/right-manage-1.vue'
  import rightManageQuality from '@/components/layout/right/right-manage/right-manage-2.vue'
  import rightManageDept from '@/components/layout/right/right-manage/right-manage-3.vue'

  import rightFunctionInvasion from '@/components/layout/right/right-function/right-function-1.vue'
  import rightFunctionAccess from '@/components/layout/right/right-function/right-function-2.vue'
  import rightFunctionMeeting from '@/components/layout/right/right-function/right-function-3.vue'

  import rightBAEnvironment from '@/components/layout/right/right-BA/right-ba-1.vue'
  import rightBAACStatus from '@/components/layout/right/right-BA/right-ba-2.vue'
  import rightBAACOperation from '@/components/layout/right/right-BA/right-ba-3.vue'

  const { toggleRight: onToggleSidebar, rightVisible: visible } = useSidebarVisible()
  const pageSettings = usePageSetting()
</script>
<style></style>
