<template>
  <div
    class="absolute group top-[4vh] left-[20vw] pt-[15px] pl-[30px] origin-left w-[27vw] h-[76vh] scale-100 z-[50]"
  >
    <deviceInfo></deviceInfo>
    <!--    <access-info></access-info>-->
    <!--    <issue></issue>-->
    <!--    <instrument-info></instrument-info>-->
  </div>
</template>
<script setup>
  import deviceInfo from '@/components/bubble/security/deviceInfo.vue'
  import AccessInfo from '@/components/bubble/access/accessInfo.vue'
  import Issue from '@/components/bubble/infoRelease/issue.vue'
  import InstrumentInfo from '@/components/bubble/energy/instrumentInfo.vue'
</script>

<style scoped></style>
