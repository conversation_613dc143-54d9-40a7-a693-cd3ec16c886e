<template>
    <div ref="chart" :style="{ width: '100%', height: '400px' }"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
    name: 'LineChart',
    props: {
        data: {
            type: Array,
            required: true,
        },
    },
    mounted() {
        this.initChart();
    },
    watch: {
        data: {
            handler() {
                this.updateChart();
            },
            deep: true,
        },
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$refs.chart);
            this.updateChart();
        },
        updateChart() {
            const option = {
                xAxis: {
                    type: 'category',
                    data: this.data.map(item => item.date),
                },
                yAxis: {
                    type: 'value',
                },
                series: [
                    {
                        data: this.data.map(item => item.count),
                        type: 'line',
                    },
                ],
            };
            this.chart.setOption(option);
        },
    },
    beforeDestroy() {
        if (this.chart) {
            this.chart.dispose();
        }
    },
};
</script>

<style scoped>
.chart {
    width: 100%;
    height: 400px;
}
</style>