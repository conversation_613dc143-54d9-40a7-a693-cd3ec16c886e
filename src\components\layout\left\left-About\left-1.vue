<template>
  <div class="box-item">
    <div class="title-tag">
      <div class="tag-box">
        <div class="num-box">
          <div class="desc_num">
            <span class="num"><span class="num-font">?</span></span
            ><span>㎡</span>
          </div>
          <div class="tag">占地面积</div>
        </div>
      </div>
      <div class="tag-box">
        <div class="num-box">
          <div class="desc_num">
            <span class="num"><span class="num-font">1</span></span
            ><span>栋</span>
          </div>
          <div class="tag">楼栋数量</div>
        </div>
      </div>
      <div class="tag-box">
        <div class="num-box">
          <div class="desc_num"><span class="num">办公</span><span></span></div>
          <div class="tag">用地性质</div>
        </div>
      </div>
    </div>
    <div class="base-des">
      市中心医院临空院区，中医特色重点医院建成。医疗床位2213张。创建国家临床重点建设专科2个、重点中医专科3个，建成省级重点专科20个。依托鄂州籍医学专家委员会共建7个医疗联合体。
    </div>
  </div>
</template>

<style scoped>
.box-item {
  height: calc(100% - (3px));
}

.title-tag {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}

.title-tag .tag-box {
  height: 50px;
  background: linear-gradient(
      90deg,
      rgba(70, 155, 255, 0.24),
      rgba(70, 155, 255, 0)
  );
  border-radius: 0.05rem 0 0 0.05rem;
  width: 33%;
  padding-left: 20px;
  display: flex;
  align-items: center;
}

.num-box .desc_num,
.num-box {
  font-size: 14px;
  text-align: left;
}

.num-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  font-style: normal;
  color: hsla(0, 0%, 100%, 0.9);
  line-height: 16px;
}

.num-box .desc_num,
.num-box {
  font-size: 14px;
  text-align: left;
}

.num-box .desc_num .num {
  margin-right: 5px;
  font-size: 18px;
  line-height: 18px;
}

.base-des {
  color: #fff;
  text-indent: 10px;
  text-align: left;
  padding: 10px;
  line-height: 15px;
}
</style>
<script setup name="AboutLeft1">
// 修改组件名称为AboutLeft1，确保唯一性
</script>
