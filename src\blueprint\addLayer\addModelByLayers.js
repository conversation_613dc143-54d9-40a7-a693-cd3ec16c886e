/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/28 16时48分32秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 图层树点击添加模型
 */
import { dracoLoaderModel } from '@/utils/3d/loader.js'
import { useLayerStore, useMainStore } from '@/store/entra/index.js'
import * as THREE from 'three'
import { h, markRaw, toRaw } from 'vue'
import { modelRotation } from '@/blueprint/other/modelRotation.js'
import { searchOnModel } from '@/utils/3d/camera.js'
import { throttle } from '@/utils/debounce.js'
import floorsButton from '@/components/locateButton/floorsButton.vue'

let flag = 0

/**
 *  是否勾选
 * @param checked check || uncheck
 * @param newArray 勾选的
 * @param removeArray 移除的
 */
function isCheckOptions(checked, newArray, removeArray) {
  if (checked.action === 'check') {
    let arr = markRaw(newArray.filter((item) => !item?.children))
    loadLayer(true, arr)
  } else if (checked.action === 'uncheck') {
    let arr = markRaw(removeArray.filter((item) => !item?.children))
    loadLayer(false, arr)
  }
}

/**
 * 添加layer
 * @param checked 是否显示
 * @param array 数组
 */
function loadLayer(checked, array) {
  const mainStore = useMainStore()
  if (checked) {
    array.forEach((item) => {
      if (item?.url) {
        dracoLoaderModel(item.url, function (gltf) {
          if (mainStore.model && mainStore.model.children) {
            let array = findDuplicateItems(mainStore.model.children)
            for (let i = array.length - 1; i >= 0; i--) {
              mainStore.model.remove(array[i])
            }
            const boolean = mainStore.model.children.find((item) => item.name === item.url)
            if (!boolean) {
              const Group = new THREE.Group()
              Group.name = item.url
              Group.add(gltf.scene)
              mainStore.model.add(Group)
              //调用一次
              // 在此回调中更新加载进度
              if (flag === 0) {
                //初始化旋转
                flag++
                // modelRotation();
              }
            }
          }
        })
      }
    })
  } else {
    array.forEach((item) => {
      if (mainStore.model && mainStore.model.children) {
        let array = findDuplicateItems(mainStore.model.children)
        for (let i = array.length - 1; i >= 0; i--) {
          mainStore.model.remove(array[i])
        }
        let group = mainStore.model.getObjectByName(item.url)
        if (group) {
          group.traverse((obj) => {
            if (!obj.isMesh) return
            //手动断开
            obj.geometry.dispose()
            obj.material.dispose()
            mainStore.model.remove(obj)
          })
          mainStore.model.remove(group)
          group = null
        }
      }
    })
  }
}

/**
 * 异步加载GLTF文件
 * @param keys keys数组
 * @param options tree返回的options
 * @param meta tree返回的options
 */
const updateCheckedKeysThrottle = (keys, options, meta) => {
  keys = keys.filter((item) => item !== '')
  let removedKeys = []
  if (meta.action === 'uncheck') {
    const removedKeys1 = getDifference(options, useLayerStore().previousCheckedKeys.value)
    const removedKeys2 = getDifference(useLayerStore().previousCheckedKeys.value, options)
    removedKeys = [...removedKeys1, ...removedKeys2]

    const ikeys = removedKeys.map((item) => item.key)
    for (let i = 0; i < useLayerStore().checkedKeys.length; i++) {
      if (ikeys.indexOf(useLayerStore().checkedKeys[i]) !== -1) {
        useLayerStore().checkedKeys = useLayerStore()
          .checkedKeys.slice(0, i)
          .concat(useLayerStore().checkedKeys.slice(i + 1))
        i-- // 调整索引以继续检查删除后的数组
      }
    }
  } else {
    options = options.filter((item) => item !== null)
    const ikeys = options.map((item) => item.key)
    ikeys.forEach((item) => {
      if (!useLayerStore().checkedKeys.includes(item)) {
        useLayerStore().checkedKeys.push(item)
      }
    })
  }
  const newlyCheckedKeys = options.filter(
    (key) => !useLayerStore().previousCheckedKeys.includes(key)
  )
  useLayerStore().previousCheckedKeys.value = [...options]
  isCheckOptions(meta, newlyCheckedKeys, removedKeys)
}

const updateCheckedKeys = throttle(updateCheckedKeysThrottle, 1000)

/**
 * 找数组的不同
 * @param arrayA 数组a
 * @param arrayB 数组b
 * @returns {*} 不同项
 */
function getDifference(arrayA, arrayB) {
  const keysB = new Set(arrayB.map((item) => item.key))
  return arrayA.filter((item) => !keysB.has(item.key))
}

// 递归查找函数
/**
 * 动态勾选tree
 * @param keys 放入keys数组
 */
function manualSelection(keys) {
  //先查询到是否需要取消的
  let layer = useLayerStore().previousCheckedKeys.value
  //如果有就取消 如果没有就继续
  //添加需要显示的节点
  const data = createData()
  const treeNode = findNodesByIds(data, keys)
  const newArray = []
  getAllNodes(treeNode, newArray)
  compareAndFilter(layer, newArray, async function (params) {
    let { a, b, c } = params
    useLayerStore().checkedKeys.length = 0
    useLayerStore().checkedKeys.push(...keys)
    //如果数组a的长度大于0就需要去掉
    if (a.length > 0) {
      let meta = {
        action: 'uncheck',
      }
      await newUpdateCheckedKeys(newArray, {
        meta: meta,
        newlyCheckedKeys: b,
        removedKeys: a,
      })
    }
    if (b.length > 0) {
      let meta = {
        action: 'check',
      }
      await newUpdateCheckedKeys(newArray, {
        meta: meta,
        newlyCheckedKeys: b,
        removedKeys: a,
      })
    }
  })
}

function newUpdateCheckedKeys(options, params) {
  useLayerStore().previousCheckedKeys.value = [...options]
  // useLayerStore().checkedKeys.value = options;
  let { meta, newlyCheckedKeys, removedKeys } = params
  isCheckOptions(meta, newlyCheckedKeys, removedKeys)
}

// updateC
/**
 * 比较tab选项中和已经加载的layer的不同
 * @param a 第一项
 * @param b 第二项
 * @param callback 回调函数
 */
function compareAndFilter(a, b, callback) {
  // 提取数组 a 和 b 中的所有 key，存储在 Set 中以便查找
  const aKeys = new Set(a.map((item) => item.key))
  const bKeys = new Set(b.map((item) => item.key))

  // 找出 a 和 b 中的相同项
  const commonItems = a.filter((item) => bKeys.has(item.key))
  if (commonItems) {
    const updatedA = a.filter((item) => !bKeys.has(item.key))
    const updatedB = b.filter((item) => !aKeys.has(item.key))
    callback({ a: updatedA, b: updatedB, boolean: true })
  } else {
    callback({ a, b, boolean: false })
  }
}

/**
 * 获取所有node 添加到新数组中
 * @param nodes node节点
 * @param allNodesArray push进去
 */
const getAllNodes = (nodes, allNodesArray) => {
  for (const node of nodes) {
    allNodesArray.push(node)
    if (node?.children) {
      getAllNodes(node.children, allNodesArray)
    }
  }
}

/**
 * 初始化tree
 * @returns {UnwrapRef<{children: [{children: [{children, label: string, key: string}], label: string, key: string}], label: string, key: string}>[]}
 */
function createData() {
  const layerConfig = useLayerStore()
  let layer = toRaw(layerConfig.layer)
  extractItemsWithChildren(layer)
  return [layer]
} //遍历data结构
/**
 * 加载定位的标签
 * @param obj tree的object对象
 */
function extractItemsWithChildren(obj) {
  if (obj.children && obj.children.length > 0) {
    obj.children.forEach((child) => extractItemsWithChildren(child))
  } else {
    // If there are no children, add the 'value' property with a value of 1
    /*  obj.prefix = () =>
            h(locateButton, {
              value: obj.url,
              onClick: function (name) {
                findOnModel(name);
              },
            });*/
    if (obj.floor) {
      /*    obj.suffix = () =>
                h(floorsButton, {
                    value: obj.url,
                    onClick: function (name) {
                        openFloors(name);
                    },
                });*/
    }
  }
}

/**
 * 通过name查询模型
 * @param name 模型名称
 */
function findOnModel(name) {
  const mainStore = useMainStore()
  let item = mainStore.model.getObjectByName(name)
  if (item) {
    searchOnModel(item)
  }
}

/**
 * 打开楼层图
 */
function openFloors(name) {
  console.log(name)
}

/**
 * 通过id寻找节点
 * @param nodes tree树nodes
 * @param ids id数组
 * @returns {*[]} 查询到的node节点
 */
const findNodesByIds = (nodes, ids) => {
  const results = []
  const findNodeById = (nodes, id) => {
    for (const node of nodes) {
      if (node.key === id) {
        return node
      }
      if (node.children) {
        const result = findNodeById(node.children, id)
        if (result) {
          return result
        }
      }
    }
    return null
  }

  for (const id of ids) {
    const result = findNodeById(nodes, id)
    if (result) {
      results.push(result)
    }
  }

  return results
}

//找到重复的
const findDuplicateItems = (arr) => {
  const nameCount = {}
  const duplicates = []

  // 统计每个 name 出现的次数
  arr.forEach((item) => {
    if (nameCount[item.name]) {
      nameCount[item.name]++
    } else {
      nameCount[item.name] = 1
    }
  })

  // 找到所有出现次数超过一次的元素，并根据规则保留适当数量的元素
  const nameTracker = {}
  arr.forEach((item) => {
    if (nameCount[item.name] > 1) {
      if (!nameTracker[item.name]) {
        nameTracker[item.name] = 0
      }
      nameTracker[item.name]++
      if (nameTracker[item.name] <= 1) {
        // 保留最多两个
        duplicates.push(item)
      }
    }
  })

  return duplicates
}

export {
  isCheckOptions,
  loadLayer,
  updateCheckedKeys,
  manualSelection,
  getAllNodes,
  findNodesByIds,
  createData,
}
