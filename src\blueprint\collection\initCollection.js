/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/6 10时57分40秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 初始化集合
 */
import { useMainStore } from '@/store/entra/index.js'
import * as THREE from 'three'
import { markRaw } from 'vue'

/**
 * 初始化集合
 */
function addCollection() {
  const mainStore = useMainStore()
  let { model, scene, underGroundSceneObject } = mainStore
  //绘制线段点的集合
  const cruisingCollection = new THREE.Group()
  cruisingCollection.name = 'cruisingCollection'
  mainStore.draw.cruisingCollection = markRaw(cruisingCollection)
  scene.add(cruisingCollection)
  //停车区域气泡信息集合
  const underGroundInfoCollection = new THREE.Group()
  underGroundInfoCollection.name = 'underGroundInfoCollection'
  mainStore.bubbles.underGroundInfoCollection = markRaw(underGroundInfoCollection)

  //停车场信息
  const underGroundCameraCollection = new THREE.Group()
  underGroundCameraCollection.name = 'underGroundCameraCollection'
  mainStore.bubbles.underGroundCameraCollection = markRaw(underGroundCameraCollection)

  //添加所有室内设置弹出层的集合
  const indoorCollection = new THREE.Group()
  indoorCollection.name = 'indoor_pop'
  mainStore.bubbles.indoorCollection = markRaw(indoorCollection)

  underGroundSceneObject.add(
    underGroundInfoCollection,
    underGroundCameraCollection,
    indoorCollection
  )
}

export { addCollection }
