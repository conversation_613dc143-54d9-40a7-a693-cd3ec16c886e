<template>
  <n-icon :component="IosApps" size="20" @click="handleClick" />
</template>

<script>
  import { defineComponent } from 'vue'
  import { NButton, NIcon } from 'naive-ui'
  import { IosApps } from '@vicons/ionicons4'

  export default defineComponent({
    name: 'floorsButton',
    computed: {
      IosApps() {
        return IosApps
      },
    },
    props: {
      value: {
        type: Number,
        required: true,
      },
    },
    emits: ['click'],
    setup(props, { emit }) {
      const handleClick = () => {
        emit('click', props.value)
      }

      return {
        handleClick,
      }
    },
    components: {
      NButton,
      NIcon,
      IosApps,
    },
  })
</script>

<style scoped>
  /* 添加必要的样式 */
</style>
