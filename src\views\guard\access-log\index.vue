<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="卡号" prop="cardNo">
        <el-input v-model="queryParams.cardNo" style="width: 200px" placeholder="请输入卡号" clearable />
      </el-form-item>
      <el-form-item label="进出标识" prop="inOutFlag">
        <el-select v-model="queryParams.inOutFlag" placeholder="请选择进出标识" clearable style="width: 200px">
          <el-option label="进入" value="0" />
          <el-option label="外出" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="卡片状态" prop="cardStatus">
        <el-select v-model="queryParams.cardStatus" placeholder="请选择卡片状态" clearable style="width: 200px">
          <el-option label="成功" value="1" />
          <el-option label="不成功" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="访问时间" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="accessLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="访问时间" align="center" prop="accessDateTime" width="160" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.accessDateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卡号" align="center" prop="cardNo" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="进出标识" align="center" prop="inOutFlag" width="100" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag :type="scope.row.inOutFlag === '0' ? 'success' : 'warning'">
            {{ scope.row.inOutFlag === '0' ? '进入' : '外出' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="卡片状态" align="center" prop="cardStatus" width="100" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag :type="scope.row.cardStatus === '1' ? 'success' : 'danger'">
            {{ scope.row.cardStatus === '1' ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="开门方式" align="center" prop="openFlag" width="120" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatOpenFlag(scope.row.openFlag) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="图片路径" align="center" prop="imagePath" width="120" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.imagePath">{{ scope.row.imagePath }}</span>
          <span v-else class="text-gray-400">无</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="logMemo" width="120" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-if="scope.row.logMemo">{{ scope.row.logMemo }}</span>
          <span v-else class="text-gray-400">无</span>
        </template>
      </el-table-column>
      <el-table-column label="修改人" align="center" prop="modifiedUser" width="100" :show-overflow-tooltip="true" />
      <el-table-column label="修改时间" align="center" prop="modifiedDateTime" width="160" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatDateTime(scope.row.modifiedDateTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination 
      v-show="total > 0" 
      :total="total" 
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" 
      @pagination="getList" 
    />
  </div>
</template>

<script setup name="GuardAccessLog">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { listGuardAccessLog } from "@/api/guard";
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();

const accessLogList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    cardNo: undefined,
    inOutFlag: undefined,
    cardStatus: undefined,
    dateRange: []
  }
});

const { queryParams } = toRefs(data);

/** 查询门禁访问记录列表 */
function getList() {
  loading.value = true;
  
  // 处理时间范围参数
  const params = { ...queryParams.value };
  if (params.dateRange && params.dateRange.length === 2) {
    params.startTime = params.dateRange[0];
    params.endTime = params.dateRange[1];
  }
  delete params.dateRange;
  
  listGuardAccessLog(params).then(response => {
    // 处理分页数据结构
    if (response.data && response.data.records && Array.isArray(response.data.records)) {
      accessLogList.value = response.data.records;
      total.value = response.data.total || 0;
    } else if (response.data && Array.isArray(response.data)) {
      // 兼容直接返回数组的情况
      accessLogList.value = response.data;
      total.value = response.data.length;
    } else {
      accessLogList.value = [];
      total.value = 0;
    }
    loading.value = false;
  }).catch(error => {
    loading.value = false;
    console.error("获取门禁访问记录失败:", error);
    ElMessage.error("获取门禁访问记录失败");
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.dateRange = [];
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  // 处理多选逻辑
}

/** 格式化日期时间 */
function formatDateTime(dateTime) {
  if (!dateTime) return '';
  return parseTime(dateTime, '{y}-{m}-{d} {h}:{i}:{s}');
}

/** 格式化开门方式 */
function formatOpenFlag(flag) {
  switch (flag) {
    case 16:  // $10 = 16
      return '刷卡开门';
    case 32:  // $20 = 32
      return '密码开门';
    case 48:  // $30 = 48
      return '非法卡';
    case 64:  // $40 = 64
      return '非法开门';
    case 80:  // $50 = 80
      return '按钮开门';
    case 96:  // $60 = 96
      return '遥控开门';
    case 112: // $70 = 112
      return '胁迫开门';
    default:
      return flag ? `未知(${flag})` : '未知';
  }
}

// 初始化数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.text-gray-400 {
  color: #9ca3af;
}
</style>
