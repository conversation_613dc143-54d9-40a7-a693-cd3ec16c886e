<template>
  <use-charts :options="option" class="h-full w-full"></use-charts>
</template>

<style scoped></style>
<script setup name="ManageRight3">
  import * as echarts from 'echarts'

  import UseCharts from '@/common/useCharts.vue'

  let salvProName = ['骨科一科', '呼吸内科', '康复医学科', '妇科一科', '骨外二科']
  let salvProValue = [239, 181, 154, 144, 135]
  let salvProMax = [] //背景按最大值
  for (let i = 0; i < salvProValue.length; i++) {
    salvProMax.push(salvProValue[0])
  }
  const option = {
    backgroundColor: '',
    grid: {
      left: '2%',
      right: '2%',
      bottom: '20%',
      top: '2%',
      containLabel: true,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
      },
      formatter: function (params) {
        return params[0].name + ' : ' + params[0].value + '万'
      },
    },
    xAxis: {
      show: false,
      type: 'value',
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLabel: {
          show: true,
          textStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        data: salvProName,
      },
      {
        type: 'category',
        inverse: true,
        axisTick: 'none',
        axisLine: 'none',
        show: true,
        axisLabel: {
          textStyle: {
            color: '#ffffff',
            fontSize: '12',
          },
        },
        data: salvProValue,
      },
    ],
    series: [
      {
        name: '值',
        type: 'bar',
        zlevel: 1,
        itemStyle: {
          normal: {
            barBorderRadius: 30,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: 'rgb(57,89,255,1)',
              },
              {
                offset: 1,
                color: 'rgb(46,200,207,1)',
              },
            ]),
          },
        },
        barWidth: 20,
        data: salvProValue,
      },
      {
        name: '背景',
        type: 'bar',
        barWidth: 20,
        barGap: '-100%',
        data: salvProMax,
        itemStyle: {
          normal: {
            color: 'rgba(24,31,68,1)',
            barBorderRadius: 30,
          },
        },
      },
    ],
  }
</script>
