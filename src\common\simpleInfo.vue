<template>
  <div class="container" v-show="visible" ref="myRef">
    <div class="header">
      <span class="triangle"></span>
      <span class="title">{{ title }}</span>
      <span class="close-btn" @click="close">X</span>
    </div>
    <div class="px-[1vw]">
      <div class="content" v-for="item in data">
        <div class="row">
          <span class="label">{{ item.name }}:</span>
          <span class="value">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

  const props = defineProps({
    data: Array,
    title: String,
    visible: {
      type: Boolean,
      required: true,
    },
  })

  const { visible, data, title, myRef, updateVisible } = toRefs(props)
  const emit = defineEmits(['updateVisible', 'closed'])

  const close = () => {
    emit('updateVisible')
  }
</script>
<style scoped>
  .container {
    padding: 5px;
    width: 300px;
    color: #fff;
    background-size: cover;
    background-image: url('@/assets/images/pop-bg.png');
  }

  .header {
    display: flex;
    align-items: center;
    height: 30px;
    padding: 1vw;
    width: 100%;
    position: relative;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
    margin-left: 20px;
  }

  .close-btn {
    cursor: pointer;
    color: red;
    font-size: 1.5rem;
    position: absolute;
    top: 10px;
    right: 10px;
  }

  .content {
  }
  .value {
  }
  .row {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
  }

  .label {
    font-weight: bold;
  }

  .triangle {
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 10px solid yellow; /* 改为 border-left */
  }
</style>
