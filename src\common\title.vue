<template>
  <div class="card">
    <div class="card-header">
      <h2>{{ title }}</h2>
    </div>
  </div>
</template>

<script>
export default {
  name: "myTitle",
  props: {
    title: {
      type: String,
      required: true,
    },
  },
};
</script>

<style scoped>
.card {
  padding: 0;
  width: 358px;
  height: 37px;
  margin-bottom: 10px;
  overflow: hidden;
  background-image: url("@/assets/images/des.png");
  background-size: 100% 100%;
  color: #fff;
}

.card-header h2 {
  font-size: 18px;
  color: #ffffff;
  line-height: 30px;
  margin-left: 90px;
  letter-spacing: 2px;
  text-shadow: 0px 0px 4px #215ca3;
  text-align: left;
}
</style>
