/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/6 15时49分19秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 动态箭头
 */
import {convertToVector3Array} from "@/utils/3d/transform.js";
import * as THREE from "three";
import arrowTexture from "@/assets/images/arrow.jpg";
import {useMainStore, useSettingStore} from "@/store/entra/index.js";
import {PathGeometry, PathPointList} from "three.path";
import {logoutRenderFunc, registerRenderFunc} from "@/utils/3d/animate.js";

/**
 * @constructor 动态箭头
 * @param coordinates 坐标
 */
function dynamicArrows(coordinates) {
    const mapSetting = useSettingStore();
    const mainStore = useMainStore();
    const {renderer, underGroundSceneObject} = mainStore;
    // 给定的坐标数组
    // const coordinates = mapSetting.testCarLeaveLine;
    // 使用转换函数
    const points = convertToVector3Array(coordinates);

    // 创建曲线
    const texLoader = new THREE.TextureLoader();
    const arrow = texLoader.load(arrowTexture);
    arrow.wrapS = THREE.RepeatWrapping;

    arrow.rotation = Math.PI;
    // 设置纹理的旋转中心，默认(0,0)
    arrow.center.set(0.5, 0.5);
    // 向异性
    arrow.anisotropy = renderer.capabilities.getMaxAnisotropy();
    const material = new THREE.MeshPhongMaterial({
        map: arrow,
        transparent: true,
        depthWrite: false,
        blending: THREE.AdditiveBlending,
    });
    // 确定一个向上的向量
    const up = new THREE.Vector3(0, 1, 0);

    // region 引入three.path包

    // 创建路径点的集合
    let pathPoints = new PathPointList();
    // 设置集合属性
    pathPoints.set(points, 0.5, 2, up, false);
    // 创建路径几何体
    const geometry = new PathGeometry();

    // 更新几何体的属性
    geometry.update(pathPoints, {
        width: 1,
        arrow: false,
    });
    // 创建路径的网格模型
    let pathToShow = new THREE.Mesh(geometry, material);
    pathToShow.name = "巡检线段";
    // 添加到场景
    underGroundSceneObject.add(pathToShow);

    // 在每一帧渲染的时候，更新贴图沿x轴的偏移量，形成uv动画效果
    registerRenderFunc("arrow", () => {
        if (!mapSetting.underGroundCarPause) {
            arrow.offset.x += +0.05;
        }
    });
}

function revertArrow() {
    const mainStore = useMainStore();
    logoutRenderFunc("arrow");
    let line = mainStore.underGroundSceneObject.getObjectByName("巡检线段");
    mainStore.underGroundSceneObject.remove(line);
}

export {dynamicArrows, revertArrow};
