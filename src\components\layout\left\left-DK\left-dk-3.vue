<template>
  <div class="item">
    <div v-for="(raf, index) in rafs" :key="index">
      <div class="left-3">
        <div class="left">
          <div class="vertical-text">
            <p>{{ index + 1 + '号监控' }}</p>
          </div>
        </div>
        <div class="right" :ref="raf"></div>
      </div>
    </div>
  </div>
</template>

<script setup name="ParkingLeft3">
  import DPlayer from 'dplayer'
  import Hls from 'hls.js'
  import { ref, onMounted } from 'vue'

  const rafs = [ref(), ref(), ref(), ref()]
  const initializePlayer = (container, url) => {
    new DPlayer({
      container: container,
      live: true,
      autoplay: true,
      theme: '#0093ff',
      loop: true,
      lang: 'zh-cn',
      screenshot: true,
      hotkey: true,
      mutex: false,
      preload: 'auto',
      volume: 0,
      video: {
        url: url,
        type: 'customHls',
        customType: {
          customHls: (video, player) => {
            const hls = new Hls()
            hls.loadSource(video.src)
            hls.attachMedia(video)
          },
        },
      },
      pluginOptions: {
        hls: {
          // hls config
        },
      },
    })
  }
  onMounted(() => {
    for (let i = 0; i < rafs.length; i++) {
      initializePlayer(rafs[i].value[0], 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8')
    }
  })
</script>

<style scoped>
  .item {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px; /* Adjust the gap between items as needed */
    font-size: 12px;
  }

  .left-3 {
    display: flex;
    flex: 1;
  }

  .left {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 83px;
    background-image: url('@/assets/images/left-3-1.png');
  }

  .vertical-text {
    writing-mode: vertical-rl; /* 竖直写字 */
    text-align: center;
    color: #fff;
  }

  .right {
    width: 140px; /* 右边 div 宽度 */
    height: 83px;
    //background-color: #00a3ff;
    background-size: cover; /* 图片尺寸适应 */
    background-position: center; /* 图片居中 */
  }
</style>
