<template>
  <a-scrollbar class="w-full max-h-[18vh] overflow-y-auto">
    <table>
      <thead>
      <tr>
        <th>告警时间</th>
        <th>位置</th>
        <th>告警信息</th>
        <th>处理状态</th>
      </tr>
      </thead>
      <tbody>
      <tr
          v-for="(item, index) in data"
          :key="index"
          :class="{ 'odd-row': index % 2 === 0, 'even-row': index % 2 !== 0 }"
      >
        <td>{{ item.time }}</td>
        <td>{{ item.location }}</td>
        <td>{{ item.info }}</td>
        <td>{{ item.status }}</td>
      </tr>
      </tbody>
    </table>
  </a-scrollbar>
</template>

<script setup name="ParkingRight3">
import aScrollbar from "@/common/scrollbar.vue";

const data = [
  {
    time: "2023-7-22 10:00",
    location: "A区",
    info: "违规停车",
    status: "未处理",
  },
  {
    time: "2023-7-22 10:00",
    location: "C区",
    info: "违规停车",
    status: "处理中",
  },
  {
    time: "2023-7-22 11:00",
    location: "D区",
    info: "烟雾报警",
    status: "未处理",
  },
  {
    time: "2023-7-22 10:00",
    location: "F区",
    info: "违规停车",
    status: "已处理",
  },
  {
    time: "2023-7-22 11:00",
    location: "F区",
    info: "违规停车",
    status: "处理中",
  },
  {
    time: "2023-7-22 10:00",
    location: "C区",
    info: "黑名单车辆",
    status: "处理中",
  },
  {
    time: "2023-7-22 11:00",
    location: "C区",
    info: "违规停车",
    status: "未处理",
  },
  {
    time: "2023-7-22 10:00",
    location: "A区",
    info: "黑名单车辆",
    status: "处理中",
  },
];
</script>

<style scoped>
table {
  width: 100%;
  border-collapse: collapse;
  color: #fff;
}

th {
  background-color: #1f4170; /* 深蓝色 */
  color: white;
  padding: 6px;
}

td {
  padding: 0.3vw;
}

tbody tr.odd-row {
  background-color: transparent;
}

tbody tr.even-row {
  background-color: #5a7ba3; /* 浅蓝色 */
}
</style>
