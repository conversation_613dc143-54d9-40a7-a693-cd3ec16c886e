# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*~

# Node.js 依赖和包管理器
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.yarn-integrity
.yarn
.pnp.*
package-lock.json
yarn.lock
pnpm-lock.yaml

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 构建输出
dist/
build/
out/
.nuxt/
.next/
.vercel/
.netlify/

# 缓存目录
.cache/
.parcel-cache/
.vite/
.rollup.cache/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 测试覆盖率和报告
coverage/
*.lcov
.nyc_output/
tests/**/coverage/
tests/e2e/reports
selenium-debug.log
lib-cov/
.grunt/

# 日志文件
**/*.log
logs/
*.log.*

# 环境配置文件
.env
.env.*
!.env.example
.envrc

# 编辑器和 IDE
.idea/
.vscode/
*.swp
*.swo
*.tmp
*.temp
*~
.project
.settings/
.classpath
.factorypath
.buildpath
.sublime-project
.sublime-workspace
*.sublime-*

# Windows 特定文件
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.local
ehthumbs_vista.db

# macOS 特定文件
.AppleDouble
.LSOverride
Icon
._*

# Linux 特定文件
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# TypeScript
*.tsbuildinfo

# ESLint 缓存
.eslintcache

# Prettier 缓存
.prettiercache

# Stylelint 缓存
.stylelintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

dist.zip