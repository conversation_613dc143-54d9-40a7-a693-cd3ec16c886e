<template>
  <div class="app-container flex flex-col w-full h-full">
    <div>
      <el-form :model="queryParams" ref="queryRef" :inline="true">
        <el-form-item label="查询日期" prop="date">
          <el-date-picker v-model="queryParams.dateTime" type="daterange" start-placeholder="开始日期"
                          end-placeholder="结束日期" format="YYYY-MM-DD" date-format="YYYY/MM/DD ddd" clearable/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex-1 w-full h-full">
      <div class="flex flex-col w-full h-auto">
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="statistic-card">
              <el-statistic :value="98500">
                <template #title>
                  <div style="display: inline-flex; align-items: center">
                    Daily active users
                    <el-tooltip effect="dark"
                                content="Number of users who logged into the product in one day"
                                placement="top">
                      <el-icon style="margin-left: 4px" :size="12">
                        <Warning/>
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-statistic>
              <div class="statistic-footer">
                <div class="footer-item">
                  <span>than yesterday</span>
                  <span class="green">
                                        24%
                                        <el-icon>
                                            <CaretTop/>
                                        </el-icon>
                                    </span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="flex-1 w-full h-full">
        123
      </div>
    </div>
  </div>
</template>
<script setup>
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    noticeTitle: undefined,
    createBy: undefined,
    status: undefined
  },

});
const {queryParams} = toRefs(data);

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
</script>
