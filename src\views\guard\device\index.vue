<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="门禁名称" prop="deviceName">
        <el-input v-model="queryParams.deviceName" style="width: 240px" placeholder="请输入门禁名称" clearable />
      </el-form-item>
      <el-form-item label="门禁地址" prop="deviceLocation">
        <el-input v-model="queryParams.deviceLocation" style="width: 240px" placeholder="请输入门禁地址" clearable />
      </el-form-item>
      <el-form-item label="设备状态" prop="deviceStatus">
        <el-select v-model="queryParams.deviceStatus" placeholder="请选择设备状态" clearable style="width: 240px">
          <el-option label="启用" value="0" />
          <el-option label="禁用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="门禁名称" align="center" prop="deviceName" :show-overflow-tooltip="true" />
      <el-table-column label="门禁地址" align="center" prop="deviceLocation" :show-overflow-tooltip="true" />
      <el-table-column label="设备类型" align="center" prop="deviceType" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatDeviceType(scope.row.deviceType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备状态" align="center" prop="deviceStatus" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag :type="scope.row.deviceStatus === '0' ? 'success' : 'danger'">
            {{ scope.row.deviceStatus === '0' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="在线状态" align="center" prop="onlineEnable" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag :type="scope.row.onlineEnable === '1' ? 'success' : 'danger'">
            {{ scope.row.onlineEnable === '1' ? '在线' : '离线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="工作模式" align="center" prop="workMode" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ formatWorkMode(scope.row.workMode) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="互锁状态" align="center" prop="lockFlag" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag :type="scope.row.lockFlag === '1' ? 'success' : 'info'">
            {{ scope.row.lockFlag === '1' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="进门读卡器" align="center" prop="inReader" :show-overflow-tooltip="true" />
      <el-table-column label="出门读卡器" align="center" prop="outReader" :show-overflow-tooltip="true" />
      <el-table-column label="组号" align="center" prop="groupNo" :show-overflow-tooltip="true" />
      <el-table-column label="延时时间" align="center" prop="devDelay" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ scope.row.devDelay }}秒</span>
        </template>
      </el-table-column>
      <el-table-column label="视频功能" align="center" prop="videoEnable" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-tag :type="scope.row.videoEnable ? 'success' : 'info'">
            {{ scope.row.videoEnable ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <pagination 
      v-show="total > 0" 
      :total="total" 
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" 
      @pagination="getList" 
    />
  </div>
</template>

<script setup name="GuardDevice">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { listGuardDevice } from "@/api/guard";

const { proxy } = getCurrentInstance();

const deviceList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deviceName: undefined,
    deviceLocation: undefined,
    deviceStatus: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询门禁设备列表 */
function getList() {
  loading.value = true;
  listGuardDevice(queryParams.value).then(response => {
    // 处理分页数据结构
    if (response.data && response.data.records && Array.isArray(response.data.records)) {
      deviceList.value = response.data.records;
      total.value = response.data.total || 0;
    } else if (response.data && Array.isArray(response.data)) {
      // 兼容直接返回数组的情况
      deviceList.value = response.data;
      total.value = response.data.length;
    } else {
      deviceList.value = [];
      total.value = 0;
    }
    loading.value = false;
  }).catch(error => {
    loading.value = false;
    console.error("获取门禁设备列表失败:", error);
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  // 处理多选逻辑
}

/** 格式化设备类型 */
function formatDeviceType(type) {
  switch (type) {
    case '0':
      return '普通门禁';
    case '9':
      return '门禁控制器';
    default:
      return type || '未知';
  }
}

/** 格式化工作模式 */
function formatWorkMode(mode) {
  switch (mode) {
    case '00':
      return '正常模式';
    case '01':
      return '常开模式';
    case '02':
      return '常闭模式';
    default:
      return mode || '未知';
  }
}

// 初始化数据
onMounted(() => {
  getList();
});
</script>
