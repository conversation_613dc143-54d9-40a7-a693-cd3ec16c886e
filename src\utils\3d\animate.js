/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/16 11时05分03秒
 * @LastEditors: du<PERSON><PERSON>hu
 * @Description: 动画
*/

import * as THREE from "three";
import {useMainStore} from "@/store/entra/index.js";

let activeAction,
    previousAction = [];

/**
 * @description 动画执行
 * @param gltfAnimate  动画对象
 * @param name 名字
 * @param duration 时间
 * @param time  当前时间
 */
export function fadeToAction(gltfAnimate, name, duration, time) {
    for (let i = 0; i < previousAction.length; i++) {
        previousAction[i] && previousAction[i].fadeOut(duration);
    }
    let {actions} = gltfAnimate;
    activeAction = actions[name];
    previousAction.push(activeAction);
    if (activeAction) {
        activeAction
            .reset()
            .setEffectiveTimeScale(1)
            .setEffectiveWeight(1)
            .fadeIn(duration)
            .play();
        if (time === 1) {
            activeAction.setLoop(THREE.LoopOnce);
        }
    }
}

/**
 *   @description 动画执行 清空版
 *  * @param gltfAnimate  动画对象
 *  * @param name 名字
 *  * @param duration 时间
 *  * @param time  当前时间
 *
 * */

export function fadeToActionNotClear(gltfAnimate, name, duration, time) {
    for (let i = 0; i < previousAction.length; i++) {
        previousAction[i] && previousAction[i].fadeOut(duration);
    }
    previousAction.length = 0;
    for (let i = 0; i < name.length; i++) {
        let {actions} = gltfAnimate;
        activeAction = actions[name[i]];
        previousAction.push(activeAction);
        if (activeAction) {
            activeAction
                .reset()
                .setEffectiveTimeScale(1)
                .setEffectiveWeight(1)
                .fadeIn(duration)
                .play();
            if (time === 1) {
                activeAction.setLoop(THREE.LoopOnce);
            }
        }
    }
}

/**
 * 注册渲染中执行的方法
 * @param name 设定函数名称
 * @param func 函数方法体
 */
export function registerRenderFunc(name, func) {
    const mainStore = useMainStore();
    mainStore.renderFunc[name] = func;
}

/**
 * 注销渲染中执行的方法
 * @param name 要注销的函数名称
 */
export function logoutRenderFunc(name) {
    const mainStore = useMainStore();
    const old = mainStore.renderFunc[name];
    if (old) {
        delete mainStore.renderFunc[name];
    }
}



