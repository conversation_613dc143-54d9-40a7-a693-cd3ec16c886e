<template>
  <div
      class="absolute  left-0 top-0 w-full bg-[url('@/assets/images/bg-title.png')] h-[5%] z-[50] bg-cover bg-no-repeat bg-center"
  >
    <div
        class="absolute bg-[url('@/assets/images/title.png')] w-[331px] h-[46px] bg-contain z-[51] bg-no-repeat top-[50%] left-[50%]"
        style="transform: translate(-50%, -50%)"
    ></div>
    <div
        class="text-[0.8vw] absolute w-[18vw] text-[#ffffff] right-[7vw] top-0 flex justify-between items-center"
    >
      <div class="text-[1vw]">{{ nowDate }}</div>
      <div class="text-[1vw]">{{ nowWeek }}</div>
      <div class="text-[1vw]">{{ nowTime }}</div>
    </div>
    <div class="absolute  right-[3vw] top-[0.5vh] text-[#fff] ">
      <n-icon
          :component="IosApps"
          class="cursor-pointer"
          size="20"
          @click="layerShow"
      />
    </div>
  </div>
</template>
<script setup>
import {onMounted, ref} from "vue";
import {NIcon} from "naive-ui";
import {IosApps} from "@vicons/ionicons4";
import {useSettingStore} from "@/store/entra/index.js";

const nowWeek = ref();
const nowDate = ref();
const nowTime = ref(new Date().toLocaleTimeString());

function getNowWeek() {
  const week = new Date().getDay();
  switch (week) {
    case 0:
      nowWeek.value = "星期日";
      break;
    case 1:
      nowWeek.value = "星期一";
      break;
    case 2:
      nowWeek.value = "星期二";
      break;
    case 3:
      nowWeek.value = "星期三";
      break;
    case 4:
      nowWeek.value = "星期四";
      break;
    case 5:
      nowWeek.value = "星期五";
      break;
    case 6:
      nowWeek.value = "星期六";
      break;
  }
}

function getNowDate() {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  nowDate.value = `${year}年${month}月${day}日`;
}

//图层显示
function layerShow() {
  const mapSetting = useSettingStore();
  let {getLayerShow} = mapSetting;
  getLayerShow.value = !getLayerShow.value;
}

onMounted(() => {
  getNowWeek();
  getNowDate();
  setInterval(() => {
    const now = new Date();
    nowTime.value = now.toLocaleTimeString();
  }, 1000);
});
</script>
<style></style>
