<template>
  <simple-info :data="data" title="设备信息" :visible="isVisible" @update:visible="updateVisible"></simple-info>
</template>


<script setup>
import {ref} from 'vue'
import SimpleInfo from "@/common/simpleInfo.vue";
import request from "@/utils/request.js";

const isVisible = ref(true);
const data = ref([
  {name: '设备编号', value: 'B-012'},
  {name: '品牌', value: '佑安'},
  {name: '类型', value: '离子试感烟'},
  {name: '设备状态', value: '在线'},
  {name: '设备周期', value: '2年'},
  {name: '购买时间', value: '2020-11-5'},
  {name: '所在位置', value: '机房1'},
])
const updateVisible = (newValue) => {
  isVisible.value = newValue;
};


</script>
<style scoped>

</style>
