import request from '@/utils/request'

// 查询停车位余位
export function getParkingSpace(query) {
    return request({
        url: '/hardware/parking/getParkSpaceInfo',
        method: 'get',
        params: query
    })
}
// 根据车牌号获取车位信息 
export function getParkingSpaceByCarNo(query) {
    return request({
        url: '/hardware/parking/getParkingSpaceInfo',
        method: 'get',
        params: query
    })
}
// 根据车位号获取车牌信息
export function getCarNoByParkingSpace(query) {
    return request({
        url: '/hardware/parking/getPlateNoInfo',
        method: 'get',
        params: query
    })
}