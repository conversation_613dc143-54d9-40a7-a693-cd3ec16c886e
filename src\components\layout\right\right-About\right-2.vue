<template>
  <use-charts :options="option" class="h-full w-full"></use-charts>
</template>

<style scoped></style>
<script setup name="AboutRight2">
import UseCharts from "@/common/useCharts.vue";

const scale = 1;
const echartData = [
  {
    value: 76,
    name: "访客",
  },
  {
    value: 257,
    name: "员工",
  },
  {
    value: 30,
    name: "其他",
  },
  {
    value: 140,
    name: "病人",
  },
];
const rich = {
  yellow: {
    color: "#ffc72b",
    fontSize: 20 * scale,
    padding: [5, 4],
    align: "center",
  },
  total: {
    color: "#ffc72b",
    fontSize: 25 * scale,
    align: "center",
  },
  white: {
    color: "#fff",
    align: "center",
    fontSize: 12 * scale,
    padding: [10, 0],
  },
  blue: {
    color: "#49dff0",
    fontSize: 12 * scale,
    align: "center",
  },
  hr: {
    borderColor: "#0b5263",
    width: "100%",
    borderWidth: 1,
    height: 0,
  },
};
const option = {
  backgroundColor: "",
  title: {
    text: "人员总数",
    left: "center",
    top: "37%",
    padding: [24, 0],
    textStyle: {
      color: "#fff",
      fontSize: 14 * scale,
      align: "center",
    },
  },
  legend: {
    selectedMode: false,
    formatter: function (name) {
      let total = 0; //各科正确率总和
      let averagePercent; //综合正确率
      echartData.forEach(function (value, index, array) {
        total += value.value;
      });
      return "{total|" + total + "}";
    },
    data: [echartData[0].name],
    left: "center",
    top: "30%",
    icon: "none",
    align: "center",
    textStyle: {
      color: "#fff",
      fontSize: 14 * scale,
      rich: rich,
    },
  },
  series: [
    {
      name: "人员总数",
      type: "pie",
      radius: ["40%", "50%"],
      center: ["50%", "40%"],
      hoverAnimation: false,
      color: ["#c487ee", "#deb140", "#49dff0", "#034079", "#6f81da", "#00ffb4"],
      label: {
        normal: {
          formatter: function (params, ticket, callback) {
            let total = 0; //考生总数量
            let percent = 0; //考生占比
            echartData.forEach(function (value, index, array) {
              total += value.value;
            });
            percent = ((params.value / total) * 100).toFixed(1);
            return (
                "{white|" +
                params.name +
                "}\n{hr|}\n{yellow|" +
                params.value +
                "}\n{blue|" +
                percent +
                "%}"
            );
          },
          rich: rich,
        },
      },
      labelLine: {
        normal: {
          length: 25 * scale,
          length2: 30,
          lineStyle: {
            color: "#0b5263",
          },
        },
      },
      data: echartData,
    },
  ],
};
</script>
