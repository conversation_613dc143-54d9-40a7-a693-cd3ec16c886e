/*
 * @Author: du<PERSON><PERSON><PERSON>
 * @Date: 2024/5/16 11时05分03秒
 * @LastEditors: duhua<PERSON>hu
 * @Description: 相机操作
 */

import { registerRenderFunc } from '@/utils/3d/animate.js'
import TWEEN from '@tweenjs/tween.js'
import * as THREE from 'three'
import { useMainStore } from '@/store/entra/index.js'
import { loadSceneWidthANDHeight } from '@/utils/3d/mouse.js'

/**
 *
 * @description 飞行定位
 * @param {*} camera 相机
 * @param {*} controls 轨道控制器
 * @param {*} targetPosition  camera position
 * @param {*} targetLookAt  control target
 * @param {*} duration 动画时长
 * @param {*} callBack 动画完成回调方法
 */
export const flyTo = (
  camera,
  controls,
  targetPosition,
  targetLookAt,
  duration,
  callBack = null
) => {
  registerRenderFunc('cameraTween', () => {
    TWEEN.update()
  })
  new TWEEN.Tween({
    // 不管相机此刻处于什么状态，直接读取当前的位置和目标观察点
    x: camera.position.x,
    y: camera.position.y,
    z: camera.position.z,
    tx: controls.target.x,
    ty: controls.target.y,
    tz: controls.target.z,
  })
    .to(
      {
        // 动画结束相机位置坐标
        x: targetPosition.x,
        y: targetPosition.y,
        z: targetPosition.z,
        // 动画结束相机指向的目标观察点
        tx: targetLookAt.x,
        ty: targetLookAt.y,
        tz: targetLookAt.z,
      },
      duration
    )
    .onUpdate(function (obj) {
      // 动态改变相机位置
      camera.position.set(obj.x, obj.y, obj.z)
      // 动态计算相机视线
      // camera.lookAt(obj.tx, obj.ty, obj.tz);
      controls.target.set(obj.tx, obj.ty, obj.tz)
      controls.update() //内部会执行.lookAt()
    })
    .start()
    .onComplete(() => {
      callBack && callBack()
    })
}

/**
 * 搜索定位
 * @param targetModel
 */
export function searchOnModel(targetModel) {
  const mainStore = useMainStore()
  let { scene, camera, renderer, controls } = mainStore
  const cubeCenter = new THREE.Vector3()
  if (targetModel) {
    targetModel.getWorldPosition(cubeCenter)
    // 设置摄像机的位置为正方体的正面视图位置
    let cameraPosition = cubeCenter.clone().add(new THREE.Vector3(0, 0, 1 * 100))
    flyTo(camera, controls, cameraPosition, cubeCenter, 2000, function () {
      controls.update()
      // 渲染场景
      renderer.render(scene, camera)
    })
  }
}

/**
 * 定位具体的坐标点
 */
export function flyToCoordinate(cameraPosition, targetPosition) {
  const mainStore = useMainStore()
  let { camera, controls } = mainStore
  flyTo(camera, controls, cameraPosition, targetPosition, 2000, function () {})
}
