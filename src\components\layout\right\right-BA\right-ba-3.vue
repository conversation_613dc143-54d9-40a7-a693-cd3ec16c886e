<template>
  <div class="contain-content">
    <table>
      <thead>
      <tr>
        <th>类别</th>
        <th>类型</th>
        <th>品牌</th>
        <th>位置</th>
        <th>状态</th>
      </tr>
      </thead>
      <tbody>
      <tr
          v-for="(item, index) in tableData"
          :key="index"
          :class="{ 'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0 }"
      >
        <td>{{ item.category }}</td>
        <td>{{ item.type }}</td>
        <td>{{ item.brand }}</td>
        <td>{{ item.location }}</td>
        <td>{{ item.status }}</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
.contain-content {
  width: 100%;
  padding: 0 1vw 3vw 1vw;
  color: #fff;
  overflow: hidden;
}

table {
  width: 100%;
  height: 100%;
}

th {
  background-color: rgba(100, 112, 127, 0.68);
  border: none;
  padding: 0.2vw;
}

td {
  padding: 0.2vw;
  border: none;
}

.even-row {
  background-color: rgba(40, 58, 105, 0.42);
}

.odd-row {
  background-color: rgba(100, 112, 127, 0.85);
}
</style>
<script setup name="BARight3">
import {ref, onMounted} from "vue";

const tableData = ref([]);

function generateRandomData() {
  const types = ["空调", "新风"];
  const brands = ["日力", "方舟", "格力"];
  const locations = ["住院楼A1栋", "住院楼B2栋", "急诊室", "门诊楼C3栋", "ICU"];
  const statuses = ["运行", "维修"];
  tableData.value.length = 0;
  for (let i = 0; i < 4; i++) {
    tableData.value.push({
      category: `类别${i + 1}`,
      type: types[0], // 类型固定为“空调”
      brand: brands[Math.floor(Math.random() * brands.length)],
      location: locations[Math.floor(Math.random() * locations.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
    });
  }
}

onMounted(() => {
  generateRandomData();
});
</script>
