<template>
  <div
      ref="scrollBox"
      class="w-full h-full custom-scrollbar overflow-y-auto"
      v-bind="$attrs"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: "CustomScrollbar",
  inheritAttrs: true,
};
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
  @apply w-2.5 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-blue-900 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-black;
}
</style>
