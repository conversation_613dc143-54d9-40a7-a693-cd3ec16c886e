<template>
  <div class="app-container w-full h-full">
    <div v-if="loading" class="w-full h-full flex items-center justify-center">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span class="ml-2">正在加载视频流...</span>
    </div>
    <div v-else class="w-full h-full rounded" ref="cameraVideo"></div>
  </div>
</template>
<script setup>
import DPlayer from 'dplayer';
import Hls from "hls.js";
import { onMounted, ref, nextTick } from "vue";
import { useRoute } from 'vue-router';
import { getCamera, getPreviewURLs } from '@/api/hardware/hkCamera';
import { Loading } from '@element-plus/icons-vue';

const route = useRoute();
const cameraVideo = ref();
const loading = ref(true);

const initializePlayer = (container, url) => {
  // 检查容器元素是否存在
  if (!container) {
    console.error('视频容器元素不存在');
    return;
  }

  // 检查HLS.js是否支持当前浏览器
  if (!Hls.isSupported()) {
    console.error('当前浏览器不支持HLS.js');
    return;
  }

  try {
    new DPlayer({
      container: container,
      live: true,
      autoplay: true,
      theme: "#0093ff",
      loop: true,
      lang: "zh-cn",
      screenshot: true,
      hotkey: true,
      preload: "auto",
      volume: 0.7,
      video: {
        url: url,
        type: "customHls",
        customType: {
          customHls: (video) => {
            const hls = new Hls({
              debug: false,
              enableWorker: true,
              lowLatencyMode: true,
              backBufferLength: 90
            });

            // 添加错误处理
            hls.on(Hls.Events.ERROR, (_, data) => {
              console.error('HLS错误:', data);
              if (data.fatal) {
                switch (data.type) {
                  case Hls.ErrorTypes.NETWORK_ERROR:
                    console.error('网络错误，尝试恢复');
                    hls.startLoad();
                    break;
                  case Hls.ErrorTypes.MEDIA_ERROR:
                    console.error('媒体错误，尝试恢复');
                    hls.recoverMediaError();
                    break;
                  default:
                    console.error('无法恢复的错误');
                    hls.destroy();
                    break;
                }
              }
            });

            hls.loadSource(video.src);
            hls.attachMedia(video);
          },
        },
      },
      mutex: false,
      pluginOptions: {
        hls: {
          // hls config
        },
      },
    });
  } catch (error) {
    console.error('初始化播放器失败:', error);
  }
};

onMounted(async () => {
  try {
    const cameraId = route.params.id;
    if (!cameraId) {
      console.error('摄像头ID不存在');
      return;
    }

    // 获取摄像头详情信息
    const cameraResponse = await getCamera(cameraId);
    if (cameraResponse && cameraResponse.data) {
      // 修复字段名错误：cameraIndexOde -> cameraIndexCode
      const cameraIndexCode = cameraResponse.data.cameraIndexCode || cameraResponse.data.cameraIndexOde;

      if (cameraIndexCode) {
        // 获取预览视频流URL
        const urlResponse = await getPreviewURLs(cameraIndexCode);

        // 修复数据访问路径：根据生产环境的数据结构 {"code":200,"data":{"url":"..."}}
        let videoUrl = null;
        if (urlResponse && urlResponse.data && urlResponse.data.url) {
          videoUrl = urlResponse.data.url;
        } else if (urlResponse && urlResponse.url) {
          // 兼容可能的其他数据结构
          videoUrl = urlResponse.url;
        }

        // 等待loading状态结束，确保DOM完全渲染
        loading.value = false;
        await nextTick();

        // 再次检查容器元素是否存在
        if (!cameraVideo.value) {
          console.error('等待DOM渲染后，容器元素仍不存在');
          return;
        }

        if (videoUrl) {
          initializePlayer(cameraVideo.value, videoUrl);
        } else {
          console.error('获取视频流URL失败');
          // 使用默认测试流作为备用
          initializePlayer(cameraVideo.value, 'http://*************:8800/hls/0/index.m3u8');
        }
      } else {
        console.error('摄像头编号不存在');
        loading.value = false;
        await nextTick();
        if (cameraVideo.value) {
          // 使用默认测试流作为备用
          initializePlayer(cameraVideo.value, 'http://*************:8800/hls/0/index.m3u8');
        }
      }
    } else {
      console.error('获取摄像头详情失败');
      loading.value = false;
      await nextTick();
      if (cameraVideo.value) {
        initializePlayer(cameraVideo.value, 'http://*************:8800/hls/0/index.m3u8');
      }
    }
  } catch (error) {
    console.error('加载摄像头视频失败:', error);
    loading.value = false;
    await nextTick();
    if (cameraVideo.value) {
      // 使用默认测试流作为备用
      initializePlayer(cameraVideo.value, 'http://*************:8800/hls/0/index.m3u8');
    }
  }
});
</script>
