<template>
  <use-charts :options="option" class="h-full w-full"></use-charts>
</template>

<style scoped></style>
<script setup>
  import UseCharts from '@/common/useCharts.vue'
  import * as echarts from 'echarts'

  const option = {
    title: {},
    backgroundColor: '',
    grid: {
      top: '20%',
      right: '10%',
      bottom: '35%', //也可设置left和right设置距离来控制图表的大小
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          show: true,
        },
      },
      formatter: function (params) {
        return `${params[0].name}<br/>
        ${params[0].seriesName}:${params[0].value}吨<br/>
        ${params[1].seriesName}:${params[1].value}Kwh`
      },
    },
    legend: {
      data: ['用水量', '用电量'],
      top: '0%',
      textStyle: {
        color: '#ffffff',
      },
    },
    xAxis: {
      data: [
        '一月',
        '二月',
        '三月',
        '四月',
        '五月',
        '六月',
        '七月',
        '八月',
        '九月',
        '十月',
        '十一月',
        '十二月',
      ],
      axisLine: {
        show: true, //隐藏X轴轴线
        lineStyle: {
          color: '#01FCE3',
        },
      },
      axisTick: {
        show: true, //隐藏X轴刻度
      },
      axisLabel: {
        rotate: -20, // 旋转45度
        margin: 15,
        interval: 0, // 每个标签都显示
        formatter: function (value) {
          // 自动换行
          return value.split(' ').join('\n')
        },
        show: true,
        textStyle: {
          color: '#ffffff', //X轴文字颜色
        },
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '吨',
        nameTextStyle: {
          color: '#ffffff',
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: true,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#FFFFFF',
          },
        },
        axisLabel: {
          show: true,
          textStyle: {
            color: '#ebf8ac',
          },
        },
      },
      {
        type: 'value',
        name: 'Kwh',
        nameTextStyle: {
          color: '#ebf8ac',
        },
        position: 'right',
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          formatter: '{value}', //右侧Y轴文字显示
          textStyle: {
            color: '#ebf8ac',
          },
        },
      },
      {
        type: 'value',
        gridIndex: 0,
        min: 50,
        max: 100,
        splitNumber: 8,
        splitLine: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(250,250,250,0.0)', 'rgba(250,250,250,0.05)'],
          },
        },
      },
    ],
    series: [
      {
        name: '用水量',
        type: 'line',
        yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
        smooth: true, //平滑曲线显示
        showAllSymbol: true, //显示所有图形。
        symbol: 'circle', //标记的图形为实心圆
        symbolSize: 10, //标记的大小
        itemStyle: {
          //折线拐点标志的样式
          color: '#058cff',
        },
        lineStyle: {
          color: '#058cff',
        },
        areaStyle: {
          color: 'rgba(5,140,255, 0.2)',
        },
        data: [132, 229.9, 51.4, 155.8, 160.2, 176.1, 145.2, 193.1, 197.2, 173.4, 153.4, 51.9],
      },
      {
        name: '用电量',
        type: 'line',
        barWidth: 15,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#00FFE3',
              },
              {
                offset: 1,
                color: '#4693EC',
              },
            ]),
          },
        },
        data: [93.3, 51.7, 194.6, 169.6, 92.6, 151.1, 72, 88, 145.7, 57, 157.3, 55.6],
      },
    ],
  }
</script>
