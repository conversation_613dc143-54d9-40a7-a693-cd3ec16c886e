/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/5/18 11时33分42秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 项目初始化执行
 */

import * as THREE from 'three'
import { useMainStore, useSettingStore } from '@/store/entra/index.js'
import { GUI } from 'three/addons/libs/lil-gui.module.min.js'
import sky from '@/assets/skybox/sky.hdr'
import { markRaw } from 'vue'
import { labelRenderer } from '@/utils/3d/toolTip.js'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'
import { BomEvent } from '@/utils/3d/eventFlow.js'
import { addCollection } from '@/blueprint/collection/initCollection.js'
import { loadSceneWidthANDHeight } from '@/utils/3d/mouse.js'
import { clickThisText } from '@/blueprint/addLayer/clickThisText.js'
import { RoomEnvironment } from 'three/addons/environments/RoomEnvironment.js'
import { RGBELoader } from 'three/addons/loaders/RGBELoader.js'
import { debounce } from '@/utils/debounce.js'

async function init() {
  const mainStore = useMainStore()
  const mapSetting = useSettingStore()
  //加载threeJs 模型
  //环境贴图
  const gui = new GUI()
  gui.domElement.style.right = '0px'
  gui.domElement.style.width = '300px'
  //隐藏gui
  gui.domElement.style.display = 'none'
  // gui.close();
  //全局gui
  mainStore.gui = markRaw(gui)

  const scene = new THREE.Scene()
  const model = new THREE.Group()
  model.name = 'modelTop'
  scene.add(model)

  //所有地库对象的集合
  const underGroundSceneObject = new THREE.Group()
  underGroundSceneObject.name = 'model_DK'
  model.add(underGroundSceneObject)
  mainStore.underGroundSceneObject = underGroundSceneObject

  //环境贴图
  /*  const hdrLoader = new RGBELoader()
  const envMap = await hdrLoader.loadAsync(sunrise)
  envMap.mapping = THREE.EquirectangularReflectionMapping
  scene.environment = envMap*/

  //添加一个纯色背景
  // 设置场景背景颜色 (例如红色)
  // scene.background = new THREE.Color("#6a6566");

  //天空盒
  const hdrLoader = new RGBELoader()
  const envMap1 = await hdrLoader.loadAsync(sky)
  envMap1.mapping = THREE.EquirectangularReflectionMapping
  scene.background = envMap1

  //平行光
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
  directionalLight.position.set(1000, -100, 900)
  scene.add(directionalLight)
  //环境光
  const ambient = new THREE.AmbientLight(0xffffff, 0.3)
  scene.add(ambient)

  //相机
  const cameraPosition = mapSetting.cameraViewOptions.init.camera
  const targetPosition = mapSetting.cameraViewOptions.init.target
  let { width, height } = loadSceneWidthANDHeight()
  const camera = new THREE.PerspectiveCamera(60, width / height, 1, 100000)
  camera.position.set(cameraPosition.x, cameraPosition.y, cameraPosition.z) //第2步：通过相机控件辅助设置OrbitControls
  camera.lookAt(targetPosition.x, targetPosition.y, targetPosition.z)
  //存储
  mainStore.scene = markRaw(scene)
  mainStore.camera = markRaw(camera)
  mainStore.model = markRaw(model)

  //创建renderer渲染器
  const sceneDom = document.getElementById('scene')
  const renderer = new THREE.WebGLRenderer({
    // alpha: true,
    antialias: true, //启用抗锯齿
    // 设置对数深度缓冲区，优化深度冲突问题
    logarithmicDepthBuffer: true,
  })
  // renderer.sortObjects = true;
  // renderer.autoClear = false;
  renderer.setSize(width, height)
  sceneDom.appendChild(renderer.domElement)
  mainStore.renderer = markRaw(renderer)
  // 设置相机控件轨道控制器OrbitControls
  const controls = new OrbitControls(camera, renderer.domElement)
  controls.target.set(targetPosition.x, targetPosition.y, targetPosition.z) //与lookAt参数保持一致
  // 禁用缩放和移动
  controls.update() //update()函数内会执行camera.lookAt(controls.target)
  //控制器
  mainStore.controls = markRaw(controls)
  //气泡添加
  let labelRendererDom
  labelRendererDom = labelRenderer(width, height)
  labelRendererDom.domElement.style.pointerEvents = 'none'
  sceneDom.appendChild(labelRendererDom.domElement)
  renderer.setPixelRatio(window.devicePixelRatio)
  //初始化时间clock
  let clock = new THREE.Clock()

  renderer.setClearColor('#000000', 1)
  // 更新到最新的 Three.js API
  renderer.toneMapping = THREE.ACESFilmicToneMapping
  renderer.outputColorSpace = THREE.SRGBColorSpace  // 替换 outputEncoding
  renderer.toneMappingExposure = 1
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap

  //环境贴图
  let pmremGenerator = new THREE.PMREMGenerator(renderer)
  pmremGenerator.compileEquirectangularShader()
  scene.environment = pmremGenerator.fromScene(new RoomEnvironment()).texture

  let renderRequested = false
  let animationFrameId = null

  function render() {
    renderRequested = false

    // 只在必要时更新控制器
    if (controls.enabled && controls.enableDamping) {
      controls.update()
    }

    renderer.render(scene, camera)
    labelRendererDom.render(scene, camera)
  }

  // 请求渲染帧
  function requestRender() {
    if (!renderRequested) {
      renderRequested = true
      animationFrameId = requestAnimationFrame(render)
    }
  }

  // 优化后的动画循环
  function animate() {
    animationFrameId = requestAnimationFrame(animate)

    // 只在有注册的渲染函数时才更新时间
    if (Object.keys(mainStore.renderFunc).length > 0) {
      const time = clock.getDelta()

      // 使用 try-catch 包装整个循环而不是每个函数调用
      try {
        Object.entries(mainStore.renderFunc).forEach(([funcName, func]) => {
          func(time)
        })
      } catch (e) {
        console.error('Animation error:', e)
      }
    }

    requestRender()
  }

  // 优化窗口大小变化处理
  const resizeHandler = debounce(() => {
    const { width, height } = loadSceneWidthANDHeight()

    camera.aspect = width / height
    camera.updateProjectionMatrix()

    renderer.setSize(width, height)
    labelRendererDom.setSize(width, height)

    requestRender()
  }, 250) // 250ms 防抖

  window.addEventListener('resize', resizeHandler)

  // 场景清理函数
  function cleanup() {
    // 停止动画循环
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }

    // 移除事件监听器
    window.removeEventListener('resize', resizeHandler)

    // 释放控制器
    if (controls) {
      controls.dispose()
    }

    // 释放渲染器
    if (renderer) {
      renderer.dispose()
    }

    // 清理场景
    scene.traverse((object) => {
      if (object.geometry) {
        object.geometry.dispose()
      }
      if (object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach((material) => material.dispose())
        } else {
          object.material.dispose()
        }
      }
    })
  }

  // 注册清理函数
  window.addEventListener('unload', cleanup)

  // 控制限制
  function limitControls() {
    controls.minPolarAngle = 0
    controls.maxPolarAngle = Math.PI / 2
    controls.maxDistance = 1500

    // 优化控制器性能
    controls.enableDamping = true
    controls.dampingFactor = 0.05
    controls.rotateSpeed = 0.5
    controls.zoomSpeed = 0.8
    controls.panSpeed = 0.5

    // 设置更新阈值，减少不必要的更新
    controls.minDistance = 10
    controls.maxDistance = 1500
  }

  // 初始化渲染
  limitControls()
  animate()
  addCollection()

  // 初始化事件系统
  mainStore.eventDom = new BomEvent(sceneDom)
  clickThisText()

  // 隐藏滚动条
  document.body.style.overflow = 'hidden'

  return cleanup // 返回清理函数供外部使用
}

export default init
