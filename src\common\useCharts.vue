<template>
  <div id="chartId" ref="chartRef" class="w-full h-full"></div>
</template>

<script setup>
import {ref, onMounted, onBeforeUnmount, watch} from "vue";
import * as echarts from "echarts";

const props = defineProps({
  options: {
    type: Object,
    required: true,
  },
  reload: {
    type: Boolean,
  },
});
const chartRef = ref(null);
let chartInstance = null;
onMounted(() => {
  chartInstance = echarts.init(chartRef.value);
  chartInstance.setOption(props.options);
  window.addEventListener("resize", resizeChart);
});

watch(
    () => props.options,
    () => {
      if (chartInstance) {
        initEcharts();
      }
    },
    {deep: true},
);

watch(
    () => props.reload,
    () => {
      if (chartInstance) {
        initEcharts();
      }
    },
);

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener("resize", resizeChart);
});

function initEcharts() {
  chartInstance.setOption(props.options);
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};
</script>
