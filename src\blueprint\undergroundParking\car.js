/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/6 09时19分32秒
 * @LastEditors: du<PERSON><PERSON><PERSON>
 * @Description: 模型汽车
 */
/*
1.添加到场景去
2.摆好位置
3.画好轨迹
4.执行动画
*/
import { dracoLoaderModel } from '@/utils/3d/loader.js'
import * as THREE from 'three'
import { useMainStore, useSettingStore } from '@/store/entra/index.js'
import { logoutRenderFunc, registerRenderFunc } from '@/utils/3d/animate.js'
import { convertToVector3Array } from '@/utils/3d/transform.js'

const Group = new THREE.Group()
Group.name = '汽车'

/**
 * 初始化机车
 * @param name 名称
 * @param position 坐标
 * @param rotationY y轴方向
 */
function addCar(name, position, rotationY) {
  const mainStore = useMainStore()
  let { gui } = mainStore
  let { 0: x, 1: y, 2: z } = position
  dracoLoaderModel(
    '/car/ferrari_sf90_stradale/scene.gltf',
    function (gltf) {
      gltf.scene.rotation.y = rotationY
      Group.add(gltf.scene)
      mainStore.underGroundSceneObject.add(Group)
    },
    name,
    {
      position: {
        x: x,
        y: y,
        z: z,
      },
    }
  )
}

function leaveCar(params) {
  const mainStore = useMainStore()
  const mapSetting = useSettingStore()
  // 定义路径点

  // 给定的坐标数组
  const coordinates = params.roadLine
  // 使用转换函数
  const points = convertToVector3Array(coordinates)
  // 创建曲线
  const curve = new THREE.CatmullRomCurve3(points)
  curve.curveType = 'centripetal'
  curve.closed = false
  // 计算总长度
  const totalDistance = curve.getLength()
  const speed = 5 // 每秒 5 米
  const totalTime = totalDistance / speed

  // 初始化时间
  let elapsedTime = 0
  const clock = new THREE.Clock()
  const car = mainStore.model.getObjectByName(params.roadName)
  registerRenderFunc('leaveCar', () => {
    const delta = clock.getDelta()
    if (!mapSetting.underGroundCarPause) {
      elapsedTime += delta
      // 计算当前进度
      const t = (elapsedTime % totalTime) / totalTime
      if (t > 0.99) {
        logoutRenderFunc('leaveCar')
      }
      // 获取路径上的点
      const position = curve.getPointAt(t)
      changeLookAt(t, car, curve)

      // 更新物体位置
      car.position.copy(position)
    }
  })
}

function changeLookAt(t, mesh, curve) {
  const mapSetting = useSettingStore()
  const tangent = curve.getTangentAt(t)
  const mainStore = useMainStore()
  let { controls, camera, scene } = mainStore
  const lookAtVec = tangent.add(mesh.position) // 位置向量和切线向量相加即为所需朝向的点向量
  // 相机的相对偏移向量, y = 1.0 让相机接近平视前方的效果， z = -5, 在NPC后5距离的位置。
  scene.rotation.y = mapSetting.initialRotationY
  /*  const finalRotationY = scene.rotation.y;
    const totalRotation = finalRotationY - mapSetting.initialRotationY;
    const rotationMatrix = new THREE.Matrix4().makeRotationY(totalRotation);
    // 重新计算向量
    const relativeCameraOffset = new THREE.Vector3(0, -4.5, -12);
    const transformedVector = relativeCameraOffset
      .clone()
      .applyMatrix4(rotationMatrix);*/
  // 转换为相对NPC世界矩阵的坐标
  const relativeCameraOffset = new THREE.Vector3(0, -4.5, -12)
  const targetCameraPosition = relativeCameraOffset.applyMatrix4(mesh.matrixWorld)

  // 在曲线上分段前进的过程中，每段的间距非常非常小，已经接近丝滑，此时不需要使用TWEEN了
  camera.position.set(targetCameraPosition.x, 0, targetCameraPosition.z)

  // 更新控制器的目标为NPC的位置
  const walkerPosition = mesh.position.clone()
  controls.target = new THREE.Vector3(walkerPosition.x, -4.5, walkerPosition.z)

  mesh.lookAt(lookAtVec)
}

function revertCar(name) {
  logoutRenderFunc('leaveCar')
  const mainStore = useMainStore()
  const mapSetting = useSettingStore()
  let car = mainStore.underGroundSceneObject.getObjectByName(name)
  let array = mapSetting.testCarLeaveLineArray
  let item = array.find((i) => i.roadName === name)
  let carCoordinates = item.roadLine[0]
  car.position.set(carCoordinates[0], carCoordinates[1], carCoordinates[2])
}

export { addCar, leaveCar, revertCar }
