<template>
  <div
    v-if="visible"
    class="absolute group top-[4vh] left-0 pt-[15px] pl-[30px] origin-left w-[27vw] h-[76vh] scale-100 z-[50] bg-cover bg-no-repeat bg-center duration-500 bg-[url('@/assets/images/left.png')]"
  >
    <div class="w-full h-1/3">
      <div v-show="pageSettings.tab.basement">
        <my-title title="基本数据"></my-title>
        <leftDKBasement></leftDKBasement>
      </div>
      <div v-show="pageSettings.tab.aboutUs">
        <my-title title="医院概览"></my-title>
        <leftAboutOverview></leftAboutOverview>
      </div>
      <div v-if="pageSettings.tab.manage">
        <my-title title="人流管理"></my-title>
      </div>
      <div v-if="pageSettings.tab.manage">
        <leftManageFlow></leftManageFlow>
      </div>
      <div v-show="pageSettings.tab.function">
        <my-title title="信息发布"></my-title>
        <leftFunctionInfo></leftFunctionInfo>
      </div>
    </div>
    <div class="w-full h-1/3 mt-[-20px]">
      <div v-if="pageSettings.tab.basement">
        <my-title title="人车数量统计"></my-title>
        <leftDKStats></leftDKStats>
      </div>
      <div v-if="pageSettings.tab.aboutUs">
        <my-title title="园区监控"></my-title>
        <leftAboutMonitor></leftAboutMonitor>
      </div>
      <div v-if="pageSettings.tab.manage">
        <my-title title="护理人员统计"></my-title>
        <leftManageNurse></leftManageNurse>
      </div>
      <div v-show="pageSettings.tab.function">
        <my-title title="电子巡检"></my-title>
        <leftFunctionInspect></leftFunctionInspect>
      </div>
      <div v-if="pageSettings.tab.baSystem">
        <!--        <leftBASecond></leftBASecond>-->
      </div>
    </div>
    <div class="w-full h-1/3 mt-[10px]">
      <div v-if="pageSettings.tab.basement">
        <my-title title="视频监控"></my-title>
        <leftDKMonitor></leftDKMonitor>
      </div>
      <div v-if="pageSettings.tab.aboutUs">
        <my-title title="能耗统计"></my-title>
      </div>
      <leftAboutEnergy v-if="pageSettings.tab.aboutUs"></leftAboutEnergy>

      <div v-if="pageSettings.tab.manage">
        <my-title title="入住床位信息"></my-title>
        <leftManageBeds></leftManageBeds>
      </div>
      <div v-if="pageSettings.tab.function">
        <my-title title="消防告警"></my-title>
        <leftFunctionAlarm></leftFunctionAlarm>
      </div>
    </div>
  </div>
  <div
    class="bg-[url('@/assets/images/leftBar.png')] bg-cover bg-no-repeat bg-center w-[27px] absolute left-0 z-[100] cursor-pointer h-full top-0"
    @click="onToggleSidebar"
  ></div>
</template>
<script setup>
  import { useSidebarVisible } from '@/hooks/useSidebar.js'
  import MyTitle from '@/common/title.vue'
  import { storeToRefs } from 'pinia'
  import leftDKBasement from '@/components/layout/left/left-DK/left-dk-1.vue'
  import leftDKStats from '@/components/layout/left/left-DK/left-dk-2.vue'
  import leftDKMonitor from '@/components/layout/left/left-DK/left-dk-3.vue'

  import leftAboutOverview from '@/components/layout/left/left-About/left-1.vue'
  import leftAboutMonitor from '@/components/layout/left/left-About/left-2.vue'
  import leftAboutEnergy from '@/components/layout/left/left-About/left-3.vue'

  import leftManageFlow from '@/components/layout/left/left-manage/left-manage-1.vue'
  import leftManageNurse from '@/components/layout/left/left-manage/left-manage-2.vue'
  import leftManageBeds from '@/components/layout/left/left-manage/left-manage-3.vue'

  import leftFunctionInfo from '@/components/layout/left/left-function/left-function-1.vue'
  import leftFunctionInspect from '@/components/layout/left/left-function/left-function-2.vue'
  import leftFunctionAlarm from '@/components/layout/left/left-function/left-function-3.vue'

  import leftBAFirst from '@/components/layout/left/left-BA/left-ba-1.vue'
  import leftBASecond from '@/components/layout/left/left-BA/left-ba-2.vue'
  import leftBAThird from '@/components/layout/left/left-BA/left-ba-3.vue'
  import { usePageSetting } from '@/store/entra/index.js'

  const { toggleLeft: onToggleSidebar, leftVisible: visible } = useSidebarVisible()
  const pageSettings = usePageSetting()
</script>

<style scoped>
  .left-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
  }

  .left-row-1 {
    width: 100%;
    margin-bottom: 20px;
  }

  .left-box-1 {
    flex: 1;
    text-align: center;
    margin-bottom: 15px;
  }

  .left-box-1:last-child {
    margin-right: 0;
  }

  .left-row-1:last-child {
    margin-bottom: 0;
  }

  .left-row-1:first-child {
    margin-top: 20px;
  }
</style>
