<template>
  <div class="app-container gap-4 flex flex-wrap flex-col">
    <div class="flex gap-4">
      <div class="w-1/2">
        <el-card class="filter-container">
          <div>
            <label for="license-plate-input">
              根据车牌号获取车位信息
            </label>
            <el-input id="license-plate-input" v-model="licensePlate" placeholder="请输入车牌号" clearable>
              <template #append>
                <el-button @click="handleLicensePlateSearch" :loading="loadingLicensePlate">查询</el-button>
              </template>
            </el-input>
          </div>
          <el-descriptions v-if="licensePlateInfo" class="info-container" column={1} border>
            <el-descriptions-item label="区域名称">{{ licensePlateInfo.area_name }}</el-descriptions-item>
            <el-descriptions-item label="楼层名称">{{ licensePlateInfo.floor_name }}</el-descriptions-item>
            <el-descriptions-item label="车牌号">{{ licensePlateInfo.plate_no }}</el-descriptions-item>
            <el-descriptions-item label="车位号">{{ licensePlateInfo.parking_space_no }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
      <div class="w-1/2">
        <el-card class="filter-container">
          <div>
            <label for="parking-space-input">
              根据车位号获取车牌信息
            </label>
            <el-input id="parking-space-input" v-model="parkingSpaceNumber" placeholder="请输入车位号" clearable>
              <template #append>
                <el-button @click="handleParkingSpaceSearch" :loading="loadingParkingSpace">查询</el-button>
              </template>
            </el-input>
          </div>
          <el-descriptions v-if="parkingSpaceInfo" class="info-container" column={1} border>
            <el-descriptions-item label="区域名称">{{ parkingSpaceInfo.area_name }}</el-descriptions-item>
            <el-descriptions-item label="楼层名称">{{ parkingSpaceInfo.floor_name }}</el-descriptions-item>
            <el-descriptions-item label="车牌号">{{ parkingSpaceInfo.plate_no }}</el-descriptions-item>
            <el-descriptions-item label="车位号">{{ parkingSpaceInfo.parking_lot_no }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
    </div>
    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :show-overflow-tooltip="true" />
      <el-table-column label="序号" align="center" prop="id" :show-overflow-tooltip="true" />
      <el-table-column label="区域名称" align="center" prop="area_name" :show-overflow-tooltip="true" />
      <el-table-column label="占用车位" align="center" prop="occupied_parking_space" :show-overflow-tooltip="true" />
      <el-table-column label="空闲车位" align="center" prop="empty_parking_space" :show-overflow-tooltip="true" />
      <el-table-column label="总车位" align="center" prop="total_parking_space" :show-overflow-tooltip="true" />
      <el-table-column label="楼层名称" align="center" prop="foor_name" :show-overflow-tooltip="true" />
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, watch } from 'vue';
import { getParkingSpace, getParkingSpaceByCarNo, getCarNoByParkingSpace } from '@/api/pull-up';
import { validateCarNumber } from '@/utils';
import { ElMessage } from 'element-plus'; // 引入 ElMessage 组件
const { proxy } = getCurrentInstance();

const recordList = ref([]);
const loading = ref(true);
const loadingLicensePlate = ref(false);
const loadingParkingSpace = ref(false);
const showSearch = ref(true);
const total = ref(0);

const licensePlate = ref('');
const parkingSpaceNumber = ref('');
const licensePlateInfo = ref(null);
const parkingSpaceInfo = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dateTime: [],
    keyword: '',
    title: undefined,
    createBy: undefined,
    status: undefined
  },
  rules: {
    title: [{ required: true, message: "公告标题不能为空", trigger: "blur" }],
    type: [{ required: true, message: "公告类型不能为空", trigger: "change" }]
  },
});
const { queryParams, resetQuery } = data;

// Watchers to clear data when input fields are cleared
watch(licensePlate, (newVal) => {
  if (!newVal) {
    licensePlateInfo.value = null;
  }
});

watch(parkingSpaceNumber, (newVal) => {
  if (!newVal) {
    parkingSpaceInfo.value = null;
  }
});

// 获取停车场余位
function getList() {
  loading.value = true;
  getParkingSpace().then(({ data: res }) => {
    const { area_info } = res;
    recordList.value = area_info.map((item, index) => ({
      id: index + 1,
      ...item
    }));
    total.value = area_info.length;
    loading.value = false;
  });
}
// 根据车牌号获取车位信息
function handleLicensePlateSearch() {
  if (!licensePlate.value) return;
  validateCarNumber(licensePlate.value, () => {
    loadingLicensePlate.value = true;
    getParkingSpaceByCarNo({ plateNo: licensePlate.value }).then(({ data: res }) => {
      licensePlateInfo.value = res;
      loadingLicensePlate.value = false;
    }).catch(() => {
      loadingLicensePlate.value = false;
    });
  });
}

function handleParkingSpaceSearch() {
  if (!parkingSpaceNumber.value) return;
  loadingParkingSpace.value = true;
  getCarNoByParkingSpace({ parkingLotNo: parkingSpaceNumber.value }).then(({ data: res }) => {
    parkingSpaceInfo.value = res;
    loadingParkingSpace.value = false;
  }).catch(() => {
    loadingParkingSpace.value = false;
  });
}

getList();
</script>

<style scoped>
.input-with-button {
  width: 100%;
}

.info-container {
  margin-top: 10px;
}
</style>
