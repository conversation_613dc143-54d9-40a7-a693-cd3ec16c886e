<template>
  <div class="containerMap">
    <n-notification-provider>
      <!--  主窗口-->
      <layout id="layoutM">
        <template #scene>
          <!--  主窗口-->
          <div id="scene"></div>
        </template>
      </layout>
    </n-notification-provider>
  </div>
</template>

<script setup>
  import init from '@/core/index.js'
  import Layout from '@/components/layout/layout.vue'
  import { onMounted } from 'vue'

  onMounted(() => {
    //初始化
    init()
  })
</script>

<style scoped>
  .main-base-style {
    padding: 0 !important;
  }
  .containerMap,
  #layoutM,
  #scene {
    min-height: calc(100vh - 84px);
    height: 100%;
  }
</style>
