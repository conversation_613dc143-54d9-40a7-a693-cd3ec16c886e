<template>
  <div class="flex">
    <div class="left bg-transparent mr-[30px]" @click="showCamera"></div>
    <div class="w-[200px] mt-[-20px]">
      <div class="hidden cameraText"></div>
      <!--      <video v-if="visibility" :src="videoSrc" autoplay controls></video>-->
      <div class="cameraClass" v-show="visibility">
        <div :ref="refName" class="w-[80%] h-[80%] rounded"></div>
        <span class="close-btn" @click="close">X</span>
      </div>
    </div>
  </div>
</template>
<script>
  // import videoSrc from "@/assets/video/A区.mp4";
  import { ref } from 'vue'
  import DPlayer from 'dplayer'
  import Hls from 'hls.js'

  export default {
    props: {
      refName: {
        type: Object,
        required: true,
      },
    },
    setup(props, { emit }) {
      const visibility = ref(false)
      let dp

      function showCamera() {
        visibility.value = !visibility.value
        if (visibility.value) {
          initializePlayer(
            props.refName.value,
            'http://cdn3.toronto360.tv:8081/toronto360/hd/playlist.m3u8'
          )
        } else {
          if (dp) {
            //暂停视频
            dp.pause()
          }
        }
      }

      const initializePlayer = (container, url) => {
        dp = new DPlayer({
          container: container,
          live: true,
          autoplay: true,
          theme: '#0093ff',
          loop: true,
          lang: 'zh-cn',
          screenshot: true,
          hotkey: true,
          preload: 'auto',
          volume: 0.7,
          video: {
            url: url,
            type: 'customHls',
            customType: {
              customHls: (video, player) => {
                const hls = new Hls()
                hls.loadSource(video.src)
                hls.attachMedia(video)
              },
            },
          },
          pluginOptions: {
            hls: {
              // hls config
            },
          },
        })
      }
      const close = () => {
        visibility.value = false
        if (dp) {
          //暂停视频
          dp.pause()
        }
      }
      return {
        showCamera,
        close,
        visibility,
        initializePlayer,
      }
    },
  }
</script>

<style scoped>
  .left {
    background-image: url('@/assets/images/local.png');
    width: 55px;
    height: 110px;
    background-size: cover;
  }
  .cameraClass {
    background-image: url('@/assets/images/pop-bg.png');
    background-size: cover;
    width: 266px;
    height: 188px;
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center;
    position: relative;
  }
  .close-btn {
    color: #fff;
    position: absolute;
    font-size: 1.5em;
    top: 10px;
    right: 7px;
  }
</style>
