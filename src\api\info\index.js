import request from "@/utils/request";

// 声音控制
export function changeVolume(params) {
  return request({
    url: "/hardware/info/changeVolume",
    method: "get",
    params,
  });
}

// 获取普通节目数据
export function getItem(params) {
  // 将前端分页参数转换为后端需要的格式
  const requestParams = {
    ...params,
    current: params.pageNo || 1,
    size: params.pageSize || 10
  };

  // 删除前端的分页参数，避免重复
  delete requestParams.pageNo;
  delete requestParams.pageSize;

  return request({
    url: "/hardware/info/getItem",
    method: "get",
    params: requestParams,
  });
}

// 获取在线终端数据
export function getOnClient(params) {
  // 将前端分页参数转换为后端需要的格式
  const requestParams = {
    ...params,
    current: params.pageNo || 1,
    size: params.pageSize || 10
  };

  // 删除前端的分页参数，避免重复
  delete requestParams.pageNo;
  delete requestParams.pageSize;

  return request({
    url: "/hardware/info/onClient",
    method: "get",
    params: requestParams,
  });
}
