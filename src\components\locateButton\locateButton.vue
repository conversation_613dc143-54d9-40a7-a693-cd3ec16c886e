<template>
  <n-icon :component="z" size="20" @click="handleClick" />
</template>

<script>
  import { defineComponent } from 'vue'
  import { NButton, NIcon } from 'naive-ui'
  import { MdLocate } from '@vicons/ionicons4'

  export default defineComponent({
    name: 'locateButton',
    computed: {
      MdLocate() {
        return MdLocate
      },
    },
    props: {
      value: {
        type: Number,
        required: true,
      },
    },
    emits: ['click'],
    setup(props, { emit }) {
      const handleClick = () => {
        emit('click', props.value)
      }

      return {
        handleClick,
      }
    },
    components: {
      NButton,
      NIcon,
      MdLocate,
    },
  })
</script>

<style scoped>
  /* 添加必要的样式 */
</style>
