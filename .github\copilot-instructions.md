## 🚀 通用 Vue 前端开发规范

### 提交规范
- 所有 Git 提交信息必须遵循 **Conventional Commits** 规范，使用中文描述。
- 提交信息应清晰、简洁，描述具体变更内容。

### 技术栈要求
- **状态管理：** 使用 **Pinia** 进行全局状态管理，模块化组织 store，放置在 `src/store/`。
- **样式：**
  - 优先使用 **Tailwind CSS** 原子化工具类。
  - 仅在 Tailwind CSS 无法满足需求时使用 `<style scoped>` 标签。
- **API 管理：**
  - 所有 API 调用必须封装在 `src/api/` 目录，按模块组织。
  - 使用 `axios` ，统一配置拦截器处理错误和认证。
- **组件要求：**
  - 组件放置在 `src/components/`，按功能模块组织。
  - 组件应遵循单一职责原则，保持高复用性。
- **代码格式化：**
  - 使用 **Prettier** 格式化代码，结合 **ESLint** 进行静态检查。
  - 配置 `.editorconfig` 确保编辑器一致性。

### 性能优化
- **事件处理：** 高频事件使用 `lodash` 的 `debounce` 或 `throttle`。
- **懒加载：** 使用动态导入实现组件懒加载。
- **虚拟列表：** 大数据列表使用 `vue-virtual-scroller` 或类似库。

### 内存管理
- 动态创建的资源在组件销毁时必须清理。
- 在 `beforeUnmount` 钩子中释放事件监听器、定时器等资源。

### 命名规范
- **组件命名：**
  - 文件名使用 PascalCase。
  - 组件内部名称与文件名一致。
- **Props 和 Emits：**
  - 使用 TypeScript 定义类型，推荐 `defineProps` 和 `defineEmits`.
- **文件和目录：** 按功能模块组织，使用 kebab-case。

### 组件分类
- **通用组件：** 放置在 `src/components/common/`。
- **业务组件：** 放置在 `src/components/business/`。
- **页面组件：** 放置在 `src/views/`。

### 代码生成指南
- 生成的代码必须完整、自然，与手动编写无明显差异。
- **禁止**使用模糊描述，如“省略其他代码”、“其余逻辑”、“后续实现”。
- 代码需符合 ESLint 和 Prettier 配置，遵循技术栈要求。
- 提供生产就绪代码，与现有代码库风格一致。

### 其他注意事项
- **安全性：** 避免 `v-html` 渲染用户输入，防止 XSS 攻击；API 请求使用 HTTPS。
- **文档：** 核心组件和模块需编写注释或 README 说明用法。