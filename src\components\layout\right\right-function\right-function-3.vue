<template>
  <div class="contain-content">
    <table>
      <thead>
      <tr>
        <th>设备类型</th>
        <th>运行状态</th>
        <th>故障状态</th>
      </tr>
      </thead>
      <tbody>
      <tr
          v-for="(item, index) in devices"
          :key="index"
          :class="{ 'even-row': index % 2 === 0, 'odd-row': index % 2 !== 0 }"
      >
        <td>{{ item.type }}</td>
        <td>{{ item.runningStatus }}</td>
        <td>{{ item.errorStatus }}</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
.contain-content {
  width: 100%;
  padding: 0 1vw 3vw 1vw;
  color: #fff;
  overflow: hidden;
}

table {
  width: 100%;
  height: 100%;
}

th {
  background-color: rgba(100, 112, 127, 0.68);
  border: none;
  padding: 0.2vw;
}

td {
  padding: 0.2vw;
  border: none;
}

.even-row {
  background-color: rgba(40, 58, 105, 0.42);
}

.odd-row {
  background-color: rgba(100, 112, 127, 0.85);
}
</style>
<script setup name="FunctionRight3">
import {ref, onMounted} from "vue";

const devices = ref([]);
const deviceTypes = ["投影机", "话筒", "音响", "大屏电视"];
const runningStatuses = ["运行", "暂停"];
const errorStatuses = ["无", "故障"];

function getRandomItem(arr) {
  return arr[Math.floor(Math.random() * arr.length)];
}

function generateDevices() {
  devices.value.length = 0;
  devices.value.push(
      ...Array.from({length: 4}, () => ({
        type: getRandomItem(deviceTypes),
        runningStatus: getRandomItem(runningStatuses),
        errorStatus: getRandomItem(errorStatuses),
      })),
  );
}

onMounted(() => {
  generateDevices();
});
</script>
