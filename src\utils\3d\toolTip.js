import { CSS3DObject, CSS3DRenderer } from 'three/addons/renderers/CSS3DRenderer.js'
import { useMainStore } from '../../store/entra/index.js'

/**
 * div插入CSS3DObject
 * @param div {HTMLElement}
 * @param x x坐标
 * @param y y坐标
 * @param z z坐标
 * @returns {CSS3DObject}
 */
const tag = (div, x, y, z) => {
  const label = new CSS3DObject(div) //div元素包装为CSS2模型对象CSS2DObject
  // 设置HTML元素标签在three.js世界坐标中位置
  label.position.set(x, y, z)

  return label //返回CSS2模型标签
}

/**
 * 创建一个CSS2渲染器CSS2DRenderer
 * @param width 画布宽度
 * @param height 画布长度
 * @returns {CSS3DRenderer}
 */
const labelRenderer = (width, height) => {
  const renderer = new CSS3DRenderer()

  renderer.setSize(width, height)
  Object.assign(renderer.domElement.style, {
    position: 'absolute',
    top: '0px',
    left: '0px',
    pointerEvents: 'none', // 避免模型标签HTML元素遮挡鼠标选择场景模型
  })

  return renderer
}

/**
 * @description 气泡组件
 * @constructor {object} Three.Group集合
 */

export class Bubble {
  constructor(collection) {
    this.mainStore = useMainStore()
    this.collection = collection
  }

  add($el, position, scale, rotation, flag = true) {
    let { bubble } = this.mainStore
    let { x, y, z } = position
    let { sx, sy } = scale
    this.bubble = bubble
    //移除上一次的气泡
    if (flag) {
      this.destroy()
    }
    bubble.labelDom = tag($el, x, y, z)
    bubble.labelDom.scale.set(sx, sy, 1) //缩放标签尺寸
    if (rotation) {
      let { x, y, z } = rotation
      bubble.labelDom.rotation.set(x, y, z)
    }
    this.collection.add(bubble.labelDom)
  }

  //气泡上的点击事件
  addListenerDomClick() {
    // pointerdown 也可以点击label
    this.mainStore.bubble.labelDom.element.addEventListener('click', function () {})
  }

  destroy() {
    let { bubble } = this.mainStore
    if (bubble.labelDom && this.collection) {
      this.collection.clear()
    }
  }
}

export { tag, labelRenderer }
